const mongoose = require('mongoose');

const PostSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Please provide a title'],
    trim: true,
    maxlength: [100, 'Title cannot be more than 100 characters']
  },
  content: {
    type: String,
    required: [true, 'Please provide content'],
    trim: true
  },
  image: {
    filename: {
      type: String,
      default: null
    },
    originalName: {
      type: String,
      default: null
    },
    contentType: {
      type: String,
      default: null
    },
    size: {
      type: Number,
      default: null
    }
  },
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

// Update the updatedAt field on save
PostSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

// Add virtual property for image URL
PostSchema.virtual('imageUrl').get(function() {
  if (this.image && this.image.filename) {
    // Make sure we have a valid filename
    const filename = this.image.filename.trim();
    if (filename) {
      return `/api/posts/image/${filename}`;
    }
  }
  return null;
});

// Configure toJSON method to include virtuals
PostSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    // Make sure imageUrl is properly set
    if (ret.image && ret.image.filename && ret.image.filename.trim()) {
      // The imageUrl virtual will be included automatically
    } else {
      ret.imageUrl = null;
    }

    // Remove the image object from the response and just keep imageUrl
    delete ret.image;

    // Convert _id to id for consistency
    if (ret._id) {
      ret.id = ret._id.toString();
      delete ret._id;
    }

    // Remove __v (version key)
    delete ret.__v;

    return ret;
  }
});

module.exports = mongoose.model('Post', PostSchema);
