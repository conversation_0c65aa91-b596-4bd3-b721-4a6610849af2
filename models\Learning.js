const mongoose = require('mongoose');

const LearningSchema = new mongoose.Schema({
  title: {
    type: String,
    required: [true, 'Please provide a title'],
    trim: true,
    maxlength: [200, 'Title cannot be more than 200 characters']
  },
  content: {
    type: String,
    required: [true, 'Please provide content'],
    trim: true
  },
  summary: {
    type: String,
    trim: true,
    maxlength: [500, 'Summary cannot be more than 500 characters']
  },
  difficulty: {
    type: String,
    enum: ['beginner', 'intermediate', 'advanced'],
    default: 'beginner'
  },
  estimatedTime: {
    type: Number,
    default: 5,
    min: [1, 'Estimated time must be at least 1 minute'],
    max: [300, 'Estimated time cannot exceed 300 minutes']
  },
  image: {
    filename: {
      type: String,
      default: null
    },
    originalName: {
      type: String,
      default: null
    },
    contentType: {
      type: String,
      default: null
    },
    size: {
      type: Number,
      default: null
    }
  },
  categories: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Category'
  }],
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  isPublished: {
    type: Boolean,
    default: true
  },
  viewCount: {
    type: Number,
    default: 0
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

LearningSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  next();
});

LearningSchema.virtual('imageUrl').get(function() {
  if (this.image && this.image.filename) {
    const filename = this.image.filename.trim();
    if (filename) {
      return `/api/learning/image/${filename}`;
    }
  }
  return null;
});

LearningSchema.set('toJSON', {
  virtuals: true,
  transform: function(doc, ret) {
    if (ret.image && ret.image.filename && ret.image.filename.trim()) {
      // The imageUrl virtual will be included automatically
    } else {
      ret.imageUrl = null;
    }

    delete ret.image;

    if (ret._id) {
      ret.id = ret._id.toString();
      delete ret._id;
    }

    delete ret.__v;
    return ret;
  }
});

LearningSchema.index({ title: 'text', content: 'text', summary: 'text' });
LearningSchema.index({ categories: 1 });
LearningSchema.index({ difficulty: 1 });
LearningSchema.index({ userId: 1 });

module.exports = mongoose.model('Learning', LearningSchema);
