<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>API Deployment Guide</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
  <link rel="stylesheet" href="css/style.css">
  <style>
    .endpoint {
      margin-bottom: 20px;
      border-left: 4px solid #007bff;
      padding-left: 15px;
    }
    .method {
      font-weight: bold;
      display: inline-block;
      padding: 3px 8px;
      border-radius: 3px;
      color: white;
      margin-right: 10px;
    }
    .method.get { background-color: #28a745; }
    .method.post { background-color: #007bff; }
    .method.put { background-color: #ffc107; color: #212529; }
    .method.delete { background-color: #dc3545; }
    .url {
      font-family: monospace;
      font-size: 1.1em;
    }
    pre {
      background-color: #f8f9fa;
      padding: 10px;
      border-radius: 5px;
    }
    .nav-pills .nav-link.active {
      background-color: #007bff;
    }
    .sidebar {
      position: sticky;
      top: 20px;
      height: calc(100vh - 40px);
      overflow-y: auto;
    }
  </style>
</head>
<body>
  <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
    <div class="container">
      <a class="navbar-brand" href="/">API Project</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav">
          <li class="nav-item">
            <a class="nav-link" href="/">Test Page</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/api-docs.html">API Documentation</a>
          </li>
          <li class="nav-item">
            <a class="nav-link active" href="/docs.html">Deployment Guide</a>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <div class="container mt-4">
    <div class="row">
      <div class="col-md-3">
        <div class="sidebar">
          <h5>Daftar Isi</h5>
          <nav class="nav flex-column nav-pills">
            <a class="nav-link" href="#persyaratan">Persyaratan Sistem</a>
            <a class="nav-link" href="#deployment">Deployment</a>
            <a class="nav-link" href="#variabel-lingkungan">Variabel Lingkungan</a>
            <a class="nav-link" href="#keamanan">Keamanan</a>
            <a class="nav-link" href="#struktur-database">Struktur Database</a>
            <a class="nav-link" href="#endpoint-auth">Endpoint: Auth</a>
            <a class="nav-link" href="#endpoint-posts">Endpoint: Posts</a>
            <a class="nav-link" href="#endpoint-comments">Endpoint: Comments</a>
            <a class="nav-link" href="#endpoint-account">Endpoint: Account</a>
            <a class="nav-link" href="#penanganan-error">Penanganan Error</a>
            <a class="nav-link" href="#pemeliharaan">Pemeliharaan</a>
          </nav>
        </div>
      </div>
      <div class="col-md-9">
        <h1 class="mb-4">Panduan Deployment API</h1>
        <p class="lead">Dokumentasi lengkap untuk API REST yang dibangun dengan Node.js, Express, dan MongoDB.</p>

        <section id="persyaratan" class="mb-5">
          <h2>Persyaratan Sistem</h2>
          <ul>
            <li>Node.js (versi 14 atau lebih tinggi)</li>
            <li>MongoDB Atlas (atau MongoDB lokal)</li>
            <li>NPM atau Yarn</li>
            <li>Git</li>
          </ul>
        </section>

        <section id="deployment" class="mb-5">
          <h2>Deployment</h2>

          <h3>Deployment Lokal</h3>
          <ol>
            <li>Clone repositori:
              <pre><code>git clone &lt;url-repositori&gt;
cd API-project</code></pre>
            </li>
            <li>Instal dependensi:
              <pre><code>npm install</code></pre>
            </li>
            <li>Buat file <code>.env</code> di root proyek berdasarkan <code>.env.example</code></li>
            <li>Jalankan server dalam mode pengembangan:
              <pre><code>npm run dev</code></pre>
            </li>
            <li>Jalankan server dalam mode produksi:
              <pre><code>npm start</code></pre>
            </li>
          </ol>

          <h3>Deployment ke Heroku</h3>
          <ol>
            <li>Buat akun Heroku dan instal Heroku CLI.</li>
            <li>Login ke Heroku CLI:
              <pre><code>heroku login</code></pre>
            </li>
            <li>Buat aplikasi Heroku:
              <pre><code>heroku create nama-aplikasi-anda</code></pre>
            </li>
            <li>Tambahkan MongoDB Atlas sebagai add-on atau gunakan layanan eksternal:
              <pre><code># Jika menggunakan MongoDB Atlas eksternal, tambahkan URI sebagai variabel lingkungan
heroku config:set MONGODB_URI=mongodb+srv://&lt;username&gt;:&lt;password&gt;@&lt;cluster&gt;.mongodb.net/&lt;database&gt;?retryWrites=true&w=majority</code></pre>
            </li>
            <li>Tambahkan variabel lingkungan lainnya:
              <pre><code>heroku config:set NODE_ENV=production
heroku config:set JWT_SECRET=your_jwt_secret_key
heroku config:set JWT_EXPIRE=30d
heroku config:set JWT_REFRESH_SECRET=your_jwt_refresh_secret_key
heroku config:set JWT_REFRESH_EXPIRE=7d</code></pre>
            </li>
            <li>Deploy aplikasi:
              <pre><code>git push heroku main</code></pre>
            </li>
            <li>Buka aplikasi:
              <pre><code>heroku open</code></pre>
            </li>
          </ol>
        </section>

        <section id="variabel-lingkungan" class="mb-5">
          <h2>Variabel Lingkungan</h2>
          <div class="table-responsive">
            <table class="table table-striped">
              <thead>
                <tr>
                  <th>Variabel</th>
                  <th>Deskripsi</th>
                  <th>Contoh</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>PORT</td>
                  <td>Port server</td>
                  <td>5000</td>
                </tr>
                <tr>
                  <td>NODE_ENV</td>
                  <td>Lingkungan aplikasi</td>
                  <td>production, development</td>
                </tr>
                <tr>
                  <td>MONGODB_URI</td>
                  <td>URI koneksi MongoDB</td>
                  <td>mongodb+srv://...</td>
                </tr>
                <tr>
                  <td>JWT_SECRET</td>
                  <td>Kunci rahasia untuk JWT</td>
                  <td>string acak</td>
                </tr>
                <tr>
                  <td>JWT_EXPIRE</td>
                  <td>Masa berlaku token JWT</td>
                  <td>30d, 24h</td>
                </tr>
                <tr>
                  <td>JWT_REFRESH_SECRET</td>
                  <td>Kunci rahasia untuk refresh token</td>
                  <td>string acak</td>
                </tr>
                <tr>
                  <td>JWT_REFRESH_EXPIRE</td>
                  <td>Masa berlaku refresh token</td>
                  <td>7d, 30d</td>
                </tr>
              </tbody>
            </table>
          </div>
        </section>

        <!-- More sections would go here -->

        <section id="endpoint-auth" class="mb-5">
          <h2>Endpoint API: Authentication</h2>

          <div class="endpoint">
            <div><span class="method post">POST</span> <span class="url">/api/auth/register</span></div>
            <p>Mendaftarkan pengguna baru</p>
            <h6>Body:</h6>
            <pre><code>{
  "username": "string",
  "email": "string",
  "password": "string"
}</code></pre>
            <h6>Response:</h6>
            <pre><code>{
  "success": true,
  "data": {
    "user": {
      "id": "string",
      "username": "string",
      "email": "string",
      "createdAt": "timestamp"
    }
  },
  "token": "string"
}</code></pre>
          </div>

          <!-- More endpoints would go here -->
        </section>

        <section id="penanganan-error" class="mb-5">
          <h2>Penanganan Error</h2>
          <p>API ini menggunakan middleware penanganan error terpusat yang menangani berbagai jenis error:</p>
          <ul>
            <li>Validation errors (400 Bad Request)</li>
            <li>Authentication errors (401 Unauthorized)</li>
            <li>Authorization errors (403 Forbidden)</li>
            <li>Not found errors (404 Not Found)</li>
            <li>Server errors (500 Internal Server Error)</li>
          </ul>

          <h6>Format respons error:</h6>
          <pre><code>{
  "success": false,
  "error": "Pesan error yang deskriptif",
  "requestId": "unique-request-id"
}</code></pre>
        </section>

        <section id="pemeliharaan" class="mb-5">
          <h2>Pemeliharaan</h2>
          <h3>Backup Database</h3>
          <p>Lakukan backup database secara berkala:</p>
          <pre><code># Untuk MongoDB Atlas, gunakan mongodump
mongodump --uri="mongodb+srv://&lt;username&gt;:&lt;password&gt;@&lt;cluster&gt;.mongodb.net/&lt;database&gt;" --out=./backup/$(date +%Y-%m-%d)</code></pre>

          <h3>Monitoring</h3>
          <p>Gunakan PM2 untuk monitoring aplikasi:</p>
          <pre><code>pm2 monit
pm2 logs</code></pre>

          <h3>Update Dependensi</h3>
          <p>Perbarui dependensi secara berkala:</p>
          <pre><code>npm outdated
npm update</code></pre>
        </section>
      </div>
    </div>
  </div>

  <footer class="bg-dark text-white mt-5 py-3">
    <div class="container text-center">
      <p class="mb-0">API Project &copy; 2023</p>
    </div>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script>
    // Activate scrollspy for navigation
    document.addEventListener('DOMContentLoaded', function() {
      const navLinks = document.querySelectorAll('.nav-link');

      // Add active class to nav links on click
      navLinks.forEach(link => {
        link.addEventListener('click', function() {
          navLinks.forEach(l => l.classList.remove('active'));
          this.classList.add('active');
        });
      });

      // Set active nav link based on scroll position
      window.addEventListener('scroll', function() {
        const sections = document.querySelectorAll('section');
        let current = '';

        sections.forEach(section => {
          const sectionTop = section.offsetTop;
          if (window.pageYOffset >= sectionTop - 100) {
            current = section.getAttribute('id');
          }
        });

        navLinks.forEach(link => {
          link.classList.remove('active');
          if (link.getAttribute('href') === `#${current}`) {
            link.classList.add('active');
          }
        });
      });
    });
  </script>
</body>
</html>
