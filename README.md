# API Project

![GitHub Workflow Status](https://img.shields.io/github/actions/workflow/status/yourusername/API-project/main.yml?branch=main)
![Node Version](https://img.shields.io/badge/node-%3E%3D14.0.0-brightgreen)
![License](https://img.shields.io/badge/license-ISC-blue)

API REST yang dibangun dengan Node.js, Express, dan MongoDB, siap untuk deployment ke GitHub.

## Daftar Isi

- [Persyaratan](#persyaratan)
- [Instalasi](#instalasi)
- [Konfigurasi](#konfigurasi)
- [Menjalankan Aplikasi](#menjalankan-aplikasi)
- [Deployment](#deployment)
  - [Deployment Lokal](#deployment-lokal)
  - [Deployment ke Heroku](#deployment-ke-heroku)
  - [Deployment ke VPS/Server](#deployment-ke-vpsserver)
  - [Deployment dengan GitHub Actions](#deployment-dengan-github-actions)
- [Struktur Proyek](#struktur-proyek)
- [Endpoint API](#endpoint-api)
- [<PERSON><PERSON>](#halaman-pengujian)
- [Keamanan](#keamanan)
- [Logging](#logging)
- [Pemeliharaan](#pemeliharaan)
- [Kontribusi](#kontribusi)
- [Lisensi](#lisensi)

## Persyaratan

- Node.js (versi 14 atau lebih tinggi)
- MongoDB Atlas (atau MongoDB lokal)
- NPM atau Yarn
- Git

## Instalasi

1. Clone repositori ini:

```bash
git clone https://github.com/yourusername/API-project.git
cd API-project
```

2. Instal dependensi:

```bash
npm install
```

## Konfigurasi

1. Salin file `.env.example` ke `.env`:

```bash
cp .env.example .env
```

2. Edit file `.env` dan sesuaikan dengan konfigurasi Anda:

```
PORT=5000
NODE_ENV=development
MONGODB_URI=mongodb+srv://<username>:<password>@<cluster>.mongodb.net/<database>?retryWrites=true&w=majority
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRE=30d
JWT_REFRESH_SECRET=your_jwt_refresh_secret_key
JWT_REFRESH_EXPIRE=7d
```

Catatan: Dalam lingkungan produksi, jangan pernah menyimpan kredensial database atau kunci rahasia JWT di repositori publik.

3. Untuk lingkungan yang berbeda, Anda dapat membuat file konfigurasi terpisah:
   - `.env.development` untuk lingkungan pengembangan
   - `.env.test` untuk lingkungan pengujian
   - `.env.production` untuk lingkungan produksi

## Menjalankan Aplikasi

1. Untuk menjalankan aplikasi dalam mode pengembangan:

```bash
npm run dev
```

2. Untuk menjalankan aplikasi dalam mode produksi:

```bash
npm start
```

3. Untuk menjalankan aplikasi dalam lingkungan tertentu:

```bash
# Mode produksi
npm run start:prod

# Mode staging
npm run start:staging
```

4. Untuk mengisi database dengan data awal:

```bash
npm run seed
```

Aplikasi akan berjalan di `http://localhost:5000`.

## Deployment

### Deployment Lokal

Ikuti langkah-langkah di bagian [Instalasi](#instalasi) dan [Konfigurasi](#konfigurasi), lalu jalankan aplikasi dengan:

```bash
npm start
```

### Deployment ke Heroku

1. Buat akun Heroku dan instal Heroku CLI.

2. Login ke Heroku CLI:

```bash
heroku login
```

3. Buat aplikasi Heroku:

```bash
heroku create nama-aplikasi-anda
```

4. Tambahkan MongoDB Atlas sebagai add-on atau gunakan layanan eksternal:

```bash
# Jika menggunakan MongoDB Atlas eksternal, tambahkan URI sebagai variabel lingkungan
heroku config:set MONGODB_URI=mongodb+srv://<username>:<password>@<cluster>.mongodb.net/<database>?retryWrites=true&w=majority
```

5. Tambahkan variabel lingkungan lainnya:

```bash
heroku config:set NODE_ENV=production
heroku config:set JWT_SECRET=your_jwt_secret_key
heroku config:set JWT_EXPIRE=30d
heroku config:set JWT_REFRESH_SECRET=your_jwt_refresh_secret_key
heroku config:set JWT_REFRESH_EXPIRE=7d
```

6. Deploy aplikasi:

```bash
git push heroku main
```

7. Buka aplikasi:

```bash
heroku open
```

### Deployment ke VPS/Server

1. Siapkan server dengan Ubuntu/Debian:

```bash
# Update sistem
sudo apt update && sudo apt upgrade -y

# Instal Node.js dan npm
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs

# Instal PM2 untuk manajemen proses
sudo npm install pm2 -g
```

2. Clone repositori:

```bash
git clone https://github.com/yourusername/API-project.git
cd API-project
```

3. Instal dependensi:

```bash
npm install --production
```

4. Buat file `.env`:

```bash
cat > .env << EOL
PORT=5000
NODE_ENV=production
MONGODB_URI=mongodb+srv://<username>:<password>@<cluster>.mongodb.net/<database>?retryWrites=true&w=majority
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRE=30d
JWT_REFRESH_SECRET=your_jwt_refresh_secret_key
JWT_REFRESH_EXPIRE=7d
EOL
```

5. Jalankan aplikasi dengan PM2:

```bash
pm2 start server.js --name api-project
pm2 save
pm2 startup
```

6. Konfigurasi Nginx sebagai reverse proxy (opsional):

```bash
sudo apt install nginx -y

# Buat konfigurasi Nginx
sudo nano /etc/nginx/sites-available/api-project

# Tambahkan konfigurasi berikut
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}

# Aktifkan konfigurasi
sudo ln -s /etc/nginx/sites-available/api-project /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### Deployment dengan GitHub Actions

Proyek ini sudah dilengkapi dengan konfigurasi GitHub Actions untuk CI/CD. Workflow akan dijalankan secara otomatis saat Anda melakukan push ke branch `main` atau `master`.

Untuk mengonfigurasi deployment otomatis:

1. Tambahkan secret yang diperlukan di repositori GitHub Anda:
   - `MONGODB_URI_TEST`: URI MongoDB untuk lingkungan pengujian
   - `JWT_SECRET_TEST`: Kunci rahasia JWT untuk lingkungan pengujian
   - `JWT_REFRESH_SECRET_TEST`: Kunci rahasia refresh token untuk lingkungan pengujian

2. Jika Anda ingin men-deploy ke platform tertentu (Heroku, AWS, Digital Ocean, dll.), uncomment dan sesuaikan bagian deployment di file `.github/workflows/main.yml`.

## Struktur Proyek

```
API-project/
├── .github/           # Konfigurasi GitHub Actions
├── config/            # Konfigurasi aplikasi
├── controllers/       # Pengendali rute
├── middleware/        # Middleware Express
├── models/            # Model Mongoose
├── public/            # File statis untuk halaman pengujian
├── routes/            # Rute API
├── scripts/           # Script utilitas (seeding, dll.)
├── utils/             # Fungsi utilitas
├── .env.example       # Contoh file konfigurasi lingkungan
├── .gitignore         # File yang diabaikan oleh Git
├── app.js             # Aplikasi Express
├── package.json       # Konfigurasi npm
├── README.md          # Dokumentasi
└── server.js          # Server entry point
```

## Endpoint API

### Autentikasi

- **Register**: `POST /api/auth/register`
  - Body: `{ "username": "string", "email": "string", "password": "string" }`
  - Response: `{ "success": true, "data": { "user": { "id": "string", "username": "string", "email": "string", "createdAt": "timestamp" } }, "token": "string" }`

- **Login**: `POST /api/auth/login`
  - Body: `{ "email": "string", "password": "string" }`
  - Response: `{ "success": true, "data": { "user": { "id": "string", "username": "string", "email": "string" } }, "token": "string" }`

- **Logout**: `POST /api/auth/logout`
  - Header: `Authorization: Bearer <token>`
  - Response: `{ "success": true, "message": "Logged out successfully" }`

- **Get User**: `GET /api/auth/user`
  - Header: `Authorization: Bearer <token>`
  - Response: `{ "success": true, "data": { "id": "string", "username": "string", "email": "string", "createdAt": "timestamp" } }`

- **Refresh Token**: `POST /api/auth/refresh`
  - Body: `{ "refreshToken": "string" }`
  - Response: `{ "success": true, "data": { "token": "string", "refreshToken": "string" } }`

### Posts

- **Get All Posts**: `GET /api/posts?page=1&limit=10&sort=-createdAt`
  - Response: `{ "success": true, "count": 10, "pagination": { ... }, "data": [ ... ] }`

- **Get Post by ID**: `GET /api/posts/:id`
  - Response: `{ "success": true, "data": { ... } }`

- **Create Post**: `POST /api/posts`
  - Header: `Authorization: Bearer <token>`
  - Body: `multipart/form-data` dengan field:
    - `title`: "string"
    - `content`: "string"
    - `image`: file gambar (opsional)
  - Response: `{ "success": true, "data": { ... } }`

- **Update Post**: `PUT /api/posts/:id`
  - Header: `Authorization: Bearer <token>`
  - Body: `multipart/form-data` dengan field:
    - `title`: "string"
    - `content`: "string"
    - `image`: file gambar baru (opsional)
  - Response: `{ "success": true, "data": { ... } }`

- **Delete Post**: `DELETE /api/posts/:id`
  - Header: `Authorization: Bearer <token>`
  - Response: `{ "success": true, "data": {} }`

- **Get Post Image**: `GET /api/posts/image/:filename`
  - Response: File gambar dengan Content-Type yang sesuai

### Comments

- **Get Comments**: `GET /api/posts/:id/comments`
  - Response: `{ "success": true, "count": 5, "data": [ ... ] }`

- **Create Comment**: `POST /api/posts/:id/comments`
  - Header: `Authorization: Bearer <token>`
  - Body: `{ "content": "string" }`
  - Response: `{ "success": true, "data": { ... } }`

- **Update Comment**: `PUT /api/posts/:id/comments/:commentId`
  - Header: `Authorization: Bearer <token>`
  - Body: `{ "content": "string" }`
  - Response: `{ "success": true, "data": { ... } }`

- **Delete Comment**: `DELETE /api/posts/:id/comments/:commentId`
  - Header: `Authorization: Bearer <token>`
  - Response: `{ "success": true, "data": {} }`

### Account

- **Get Account**: `GET /api/account`
  - Header: `Authorization: Bearer <token>`
  - Response: `{ "success": true, "data": { ... } }`

- **Update Account**: `PUT /api/account`
  - Header: `Authorization: Bearer <token>`
  - Body: `{ "username": "string", "email": "string" }`
  - Response: `{ "success": true, "data": { ... } }`

- **Delete Account**: `DELETE /api/account`
  - Header: `Authorization: Bearer <token>`
  - Response: `{ "success": true, "data": {} }`

### Categories

- **Get All Categories**: `GET /api/categories?active=true`
  - Response: `{ "success": true, "count": 5, "data": [ ... ] }`

- **Get Category by ID**: `GET /api/categories/:id`
  - Response: `{ "success": true, "data": { ... } }`

- **Create Category**: `POST /api/categories`
  - Header: `Authorization: Bearer <token>`
  - Body: `{ "name": "string", "description": "string", "color": "#3B82F6", "icon": "string" }`
  - Response: `{ "success": true, "data": { ... } }`

- **Update Category**: `PUT /api/categories/:id`
  - Header: `Authorization: Bearer <token>`
  - Body: `{ "name": "string", "description": "string", "color": "#3B82F6", "icon": "string" }`
  - Response: `{ "success": true, "data": { ... } }`

- **Delete Category**: `DELETE /api/categories/:id`
  - Header: `Authorization: Bearer <token>`
  - Response: `{ "success": true, "data": {} }`

### Learning Content

- **Get All Learning Content**: `GET /api/learning?page=1&limit=10&sort=-createdAt&category=:categoryId&difficulty=beginner&search=keyword&published=true`
  - Response: `{ "success": true, "count": 10, "pagination": { ... }, "data": [ ... ] }`

- **Get Learning Content by ID**: `GET /api/learning/:id`
  - Response: `{ "success": true, "data": { ..., "userProgress": { ... } } }`

- **Create Learning Content**: `POST /api/learning`
  - Header: `Authorization: Bearer <token>`
  - Body: `multipart/form-data` dengan field:
    - `title`: "string"
    - `content`: "string" (konten panjang)
    - `summary`: "string"
    - `difficulty`: "beginner|intermediate|advanced"
    - `estimatedTime`: number (dalam menit)
    - `categories`: "array" (JSON string dari array ID kategori)
    - `image`: file gambar (opsional)
    - `isPublished`: boolean
  - Response: `{ "success": true, "data": { ... } }`

- **Update Learning Content**: `PUT /api/learning/:id`
  - Header: `Authorization: Bearer <token>`
  - Akses: Hanya pemilik konten
  - Body: `multipart/form-data` dengan field yang sama seperti create
  - Response: `{ "success": true, "data": { ... } }`

- **Delete Learning Content**: `DELETE /api/learning/:id`
  - Header: `Authorization: Bearer <token>`
  - Akses: Hanya pemilik konten
  - Response: `{ "success": true, "data": {} }`

- **Get Learning Content Image**: `GET /api/learning/image/:filename`
  - Response: File gambar dengan Content-Type yang sesuai

- **Get Learning Content by Category**: `GET /api/learning/category/:categoryId?page=1&limit=10&sort=-createdAt`
  - Response: `{ "success": true, "count": 10, "pagination": { ... }, "data": [ ... ] }`

### Learning Progress & Bookmarks

- **Bookmark Learning Content**: `POST /api/learning/:id/bookmark`
  - Header: `Authorization: Bearer <token>`
  - Response: `{ "success": true, "data": { ... } }`

- **Remove Bookmark**: `DELETE /api/learning/:id/bookmark`
  - Header: `Authorization: Bearer <token>`
  - Response: `{ "success": true, "data": { ... } }`

- **Get User Bookmarks**: `GET /api/learning/bookmarks?page=1&limit=10`
  - Header: `Authorization: Bearer <token>`
  - Response: `{ "success": true, "count": 5, "pagination": { ... }, "data": [ ... ] }`

- **Mark as Completed**: `POST /api/learning/:id/complete`
  - Header: `Authorization: Bearer <token>`
  - Response: `{ "success": true, "data": { ... } }`

- **Update Reading Progress**: `PUT /api/learning/:id/progress`
  - Header: `Authorization: Bearer <token>`
  - Body: `{ "readingProgress": number (0-100) }`
  - Response: `{ "success": true, "data": { ... } }`

- **Get User Progress**: `GET /api/learning/progress?page=1&limit=10&completed=true`
  - Header: `Authorization: Bearer <token>`
  - Response: `{ "success": true, "count": 5, "pagination": { ... }, "stats": { ... }, "data": [ ... ] }`

## Halaman Pengujian

Proyek ini menyertakan halaman pengujian yang dapat diakses di `http://localhost:5000` setelah menjalankan server. Halaman ini memungkinkan Anda untuk menguji semua endpoint API melalui antarmuka pengguna yang ramah.

Fitur halaman pengujian:

1. Registrasi dan login pengguna
2. Membuat, melihat, mengedit, dan menghapus post
3. Menambahkan, melihat, mengedit, dan menghapus komentar
4. Mengelola akun pengguna
5. Mengelola kategori pembelajaran
6. Membuat, melihat, mengedit, dan menghapus konten pembelajaran
7. Bookmark dan tracking progress pembelajaran
8. Filter konten berdasarkan kategori dan tingkat kesulitan

Untuk menggunakan halaman pengujian:

1. Buka `http://localhost:5000` di browser Anda
2. Daftar akun baru atau masuk dengan akun yang sudah ada
3. Gunakan formulir dan tombol yang disediakan untuk berinteraksi dengan API

Semua respons API akan ditampilkan di bagian bawah halaman.

## Keamanan

API ini mengimplementasikan beberapa fitur keamanan:

1. **Helmet** - Mengamankan aplikasi Express dengan berbagai HTTP header
2. **Rate Limiting** - Membatasi jumlah permintaan dari satu IP
3. **JWT Authentication** - Mengamankan endpoint dengan JSON Web Tokens
4. **Password Hashing** - Menggunakan bcrypt untuk hashing password
5. **CORS** - Mengonfigurasi Cross-Origin Resource Sharing
6. **Input Validation** - Validasi input untuk mencegah injeksi

## Logging

Sistem logging terpusat diimplementasikan untuk memudahkan debugging dan monitoring:

- Log level yang berbeda (error, warn, info, debug)
- Format log yang konsisten dengan timestamp
- Request ID untuk melacak permintaan
- Logging khusus untuk lingkungan pengembangan dan produksi

## Pemeliharaan

### Backup Database

Lakukan backup database secara berkala:

```bash
# Untuk MongoDB Atlas, gunakan mongodump
mongodump --uri="mongodb+srv://<username>:<password>@<cluster>.mongodb.net/<database>" --out=./backup/$(date +%Y-%m-%d)
```

### Monitoring

Gunakan PM2 untuk monitoring aplikasi:

```bash
pm2 monit
pm2 logs
```

### Update Dependensi

Perbarui dependensi secara berkala:

```bash
npm outdated
npm update
```

## Kontribusi

Kontribusi selalu diterima! Silakan buat pull request atau buka issue untuk perbaikan atau fitur baru.

## Lisensi

Proyek ini dilisensikan di bawah lisensi ISC.
