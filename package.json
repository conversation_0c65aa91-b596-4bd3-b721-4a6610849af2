{"name": "api-project", "version": "1.0.1", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "start:prod": "NODE_ENV=production node server.js", "start:staging": "NODE_ENV=staging node server.js", "test": "echo \"No tests configured yet\"", "lint": "echo \"No linting configured\"", "seed": "node scripts/seed.js", "seed:prod": "NODE_ENV=production node scripts/seed.js", "prepare": "npm run lint"}, "keywords": ["api", "rest", "express", "mongodb", "nodejs"], "author": "", "license": "ISC", "description": "REST API built with Node.js, Express, and MongoDB", "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "git+https://github.com/yourusername/API-project.git"}, "dependencies": {"bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.0.3", "express": "^4.18.2", "express-rate-limit": "^6.7.0", "gridfs-stream": "^1.1.1", "helmet": "^6.1.5", "jsonwebtoken": "^9.0.0", "mongoose": "^7.0.3", "multer": "^1.4.4", "multer-gridfs-storage": "^5.0.2"}, "devDependencies": {"nodemon": "^3.1.10"}}