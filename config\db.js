const mongoose = require('mongoose');
const config = require('./config');
const logger = require('../utils/logger');

/**
 * Connect to MongoDB with retry logic
 */
const connectDB = async () => {
  const { uri, options } = config.db;
  let retries = 5;
  let connected = false;

  while (retries > 0 && !connected) {
    try {
      const conn = await mongoose.connect(uri, {
        ...options,
        serverSelectionTimeoutMS: 5000, // Timeout after 5s instead of 30s
      });

      connected = true;
      logger.info(`MongoDB Connected: ${conn.connection.host}`);

      // Add connection event listeners
      mongoose.connection.on('error', (err) => {
        logger.error('MongoDB connection error:', { error: err.message });
      });

      mongoose.connection.on('disconnected', () => {
        logger.warn('MongoDB disconnected. Attempting to reconnect...');
      });

      mongoose.connection.on('reconnected', () => {
        logger.info('MongoDB reconnected successfully');
      });

      // Handle application termination
      process.on('SIGINT', async () => {
        await mongoose.connection.close();
        logger.info('MongoDB connection closed due to app termination');
        process.exit(0);
      });

      return conn;
    } catch (error) {
      retries -= 1;
      logger.error(`MongoDB Connection Error: ${error.message}`, {
        retriesLeft: retries,
        uri: uri.replace(/\/\/([^:]+):([^@]+)@/, '//***:***@') // Hide credentials in logs
      });

      if (retries === 0) {
        logger.error('Failed to connect to MongoDB after multiple attempts');
        process.exit(1);
      }

      // Wait before retrying
      logger.info(`Retrying connection in 5 seconds... (${retries} attempts left)`);
      await new Promise(resolve => setTimeout(resolve, 5000));
    }
  }
};

module.exports = connectDB;
