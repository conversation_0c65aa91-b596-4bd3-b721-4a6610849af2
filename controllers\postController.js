const Post = require('../models/Post');
const { getImage, streamImage, deleteImage } = require('../utils/imageUpload');
const logger = require('../utils/logger');

// @desc    Get all posts
// @route   GET /api/posts
// @access  Public
exports.getAllPosts = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, sort = '-createdAt' } = req.query;

    // Convert page and limit to numbers
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);

    // Calculate skip value for pagination
    const skip = (pageNum - 1) * limitNum;

    // Find posts with pagination
    const posts = await Post.find()
      .sort(sort)
      .skip(skip)
      .limit(limitNum)
      .populate('userId', 'username');

    // Get total count of posts
    const total = await Post.countDocuments();

    res.status(200).json({
      success: true,
      count: posts.length,
      pagination: {
        total,
        page: pageNum,
        limit: limitNum,
        pages: Math.ceil(total / limitNum)
      },
      data: posts
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get single post
// @route   GET /api/posts/:id
// @access  Public
exports.getPostById = async (req, res, next) => {
  try {
    const post = await Post.findById(req.params.id).populate('userId', 'username');

    if (!post) {
      return res.status(404).json({
        success: false,
        error: 'Post not found'
      });
    }

    res.status(200).json({
      success: true,
      data: post
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Create new post
// @route   POST /api/posts
// @access  Private
exports.createPost = async (req, res, next) => {
  try {
    // Add user to req.body
    req.body.userId = req.user.id;

    // Create post data object
    const postData = {
      title: req.body.title,
      content: req.body.content,
      userId: req.user.id
    };

    // Handle image if uploaded
    if (req.file) {
      // Get file details
      postData.image = {
        filename: req.file.filename,
        originalName: req.file.originalname,
        contentType: req.file.contentType || req.file.mimetype,
        size: req.file.size
      };

      logger.info(`Image uploaded for post: ${req.file.filename}`);
    }

    // Create post
    const post = await Post.create(postData);

    res.status(201).json({
      success: true,
      data: post
    });
  } catch (err) {
    // If there was an error and we uploaded a file, delete it
    if (req.file && req.file.filename) {
      try {
        await deleteImage(req.file.filename);
        logger.info(`Deleted image ${req.file.filename} due to post creation error`);
      } catch (deleteErr) {
        logger.error('Error deleting image after post creation failure', {
          error: deleteErr.message,
          filename: req.file.filename
        });
      }
    }

    next(err);
  }
};

// @desc    Update post
// @route   PUT /api/posts/:id
// @access  Private (owner only)
exports.updatePost = async (req, res, next) => {
  try {
    let post = await Post.findById(req.params.id);

    if (!post) {
      return res.status(404).json({
        success: false,
        error: 'Post not found'
      });
    }

    // Create update data object
    const updateData = {
      title: req.body.title,
      content: req.body.content
    };

    // Handle image if uploaded
    if (req.file) {
      // Get file details
      updateData.image = {
        filename: req.file.filename,
        originalName: req.file.originalname,
        contentType: req.file.contentType || req.file.mimetype,
        size: req.file.size
      };

      logger.info(`New image uploaded for post update: ${req.file.filename}`);

      // Delete old image if exists
      if (post.image && post.image.filename) {
        try {
          await deleteImage(post.image.filename);
          logger.info(`Deleted old image ${post.image.filename} during post update`);
        } catch (deleteErr) {
          logger.error('Error deleting old image during post update', {
            error: deleteErr.message,
            filename: post.image.filename
          });
          // Continue with update even if old image deletion fails
        }
      }
    }

    post = await Post.findByIdAndUpdate(req.params.id, updateData, {
      new: true,
      runValidators: true
    });

    res.status(200).json({
      success: true,
      data: post
    });
  } catch (err) {
    // If there was an error and we uploaded a file, delete it
    if (req.file && req.file.filename) {
      try {
        await deleteImage(req.file.filename);
        logger.info(`Deleted image ${req.file.filename} due to post update error`);
      } catch (deleteErr) {
        logger.error('Error deleting image after post update failure', {
          error: deleteErr.message,
          filename: req.file.filename
        });
      }
    }

    next(err);
  }
};

// @desc    Delete post
// @route   DELETE /api/posts/:id
// @access  Private (owner only)
exports.deletePost = async (req, res, next) => {
  try {
    const post = await Post.findById(req.params.id);

    if (!post) {
      return res.status(404).json({
        success: false,
        error: 'Post not found'
      });
    }

    // Delete associated image if exists
    if (post.image && post.image.filename) {
      try {
        await deleteImage(post.image.filename);
        logger.info(`Deleted image ${post.image.filename} during post deletion`);
      } catch (deleteErr) {
        logger.error('Error deleting image during post deletion', {
          error: deleteErr.message,
          filename: post.image.filename
        });
        // Continue with post deletion even if image deletion fails
      }
    }

    await post.deleteOne();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get post image
// @route   GET /api/posts/image/:filename
// @access  Public
exports.getPostImage = async (req, res) => {
  try {
    // Set CORS headers specifically for image routes
    res.set('Access-Control-Allow-Origin', '*');
    res.set('Cross-Origin-Resource-Policy', 'cross-origin');
    
    // Then stream the image
    await streamImage(req.params.filename, res);
  } catch (err) {
    logger.error('Error retrieving image', { error: err.message, filename: req.params.filename });
    return res.status(500).json({
      success: false,
      error: 'Error retrieving image'
    });
  }
};

