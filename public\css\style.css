/* Global Styles */
body {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.container {
  max-width: 1200px;
}

/* Header Styles */
.navbar {
  background: rgba(0, 0, 0, 0.9) !important;
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
  font-weight: 700;
  font-size: 1.5rem;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Card Styles */
.card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  margin-bottom: 2rem;
  animation: fadeInUp 0.6s ease-out;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.card-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 15px 15px 0 0 !important;
  border: none;
  padding: 1.5rem;
}

.card-header h2, .card-header h5 {
  margin: 0;
  font-weight: 600;
}

.card-body {
  padding: 2rem;
}

/* Form Styles */
.form-control {
  border: 2px solid #e9ecef;
  border-radius: 10px;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.9);
}

.form-control:focus {
  border-color: #667eea;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
  background: white;
}

.form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

/* Button Styles */
.btn {
  border-radius: 10px;
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  transition: all 0.3s ease;
  margin-right: 0.5rem;
  margin-bottom: 0.5rem;
  border: none;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
}

.btn-primary:hover {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-info {
  background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
  border: none;
}

.btn-info:hover {
  background: linear-gradient(135deg, #2bc4d1 0%, #4a75d3 100%);
  transform: translateY(-2px);
}

.btn-warning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border: none;
}

.btn-warning:hover {
  background: linear-gradient(135deg, #ee82f9 0%, #f3455a 100%);
  transform: translateY(-2px);
}

.btn-danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  border: none;
}

.btn-danger:hover {
  background: linear-gradient(135deg, #ff5252 0%, #e53e3e 100%);
  transform: translateY(-2px);
}

.btn-success {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
  border: none;
}

.btn-success:hover {
  background: linear-gradient(135deg, #3cbab2 0%, #3a8f7b 100%);
  transform: translateY(-2px);
}

/* Response Area */
#response {
  max-height: 400px;
  overflow-y: auto;
  white-space: pre-wrap;
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 10px;
  padding: 1.5rem;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
  line-height: 1.5;
}

/* Posts List Styles */
.list-group-item {
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 10px;
  margin-bottom: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
}

.list-group-item:hover {
  background: white;
  transform: translateX(5px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.list-group-item h5 {
  color: #495057;
  font-weight: 700;
  margin-bottom: 1rem;
}

.list-group-item p {
  color: #6c757d;
  line-height: 1.6;
  margin-bottom: 1rem;
}

.list-group-item small {
  color: #adb5bd;
  font-weight: 500;
}

/* Empty list group styling */
.list-group:empty {
  display: none;
}

.list-group:empty + .mt-2,
.list-group:empty + .mt-3 {
  margin-top: 0 !important;
}

/* Hide empty containers that might create invisible space */
#accountDetails:empty,
#categoriesList:empty,
#progressList:empty,
#learningList:empty,
#postsList:empty,
#commentsList:empty {
  display: none;
}

/* Ensure empty divs don't create spacing */
div:empty {
  margin: 0;
  padding: 0;
  height: 0;
  min-height: 0;
}

/* Image Styles */
.post-image {
  border-radius: 10px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  margin-bottom: 1rem;
}

.post-image:hover {
  transform: scale(1.02);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Pre and Code Styles */
pre {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: 2px solid #dee2e6;
  border-radius: 10px;
  padding: 1.5rem;
  overflow-x: auto;
}

/* Tab Styles */
.nav-tabs {
  border-bottom: 2px solid #dee2e6;
  margin-bottom: 2rem;
}

.nav-tabs .nav-link {
  border: none;
  border-radius: 10px 10px 0 0;
  color: #6c757d;
  font-weight: 600;
  padding: 1rem 1.5rem;
  transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

.nav-tabs .nav-link.active {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
}

/* Footer Styles */
footer {
  background: rgba(0, 0, 0, 0.9) !important;
  backdrop-filter: blur(10px);
}

/* Animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}



/* Loading States */
.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn.loading {
  position: relative;
  color: transparent;
}

.btn.loading::after {
  content: "";
  position: absolute;
  width: 16px;
  height: 16px;
  top: 50%;
  left: 50%;
  margin-left: -8px;
  margin-top: -8px;
  border: 2px solid #ffffff;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Success/Error Messages */
.alert {
  border-radius: 10px;
  border: none;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.alert-success {
  background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
  color: white;
}

.alert-danger {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  color: white;
}

.alert-info {
  background: linear-gradient(135deg, #36d1dc 0%, #5b86e5 100%);
  color: white;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 3rem 1rem;
  color: #6c757d;
}

.empty-state i {
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.5;
}

/* Hover Effects */

.list-group-item:hover .btn {
  transform: scale(1.05);
}

/* Badge Styles */
.badge {
  border-radius: 20px;
  padding: 0.5rem 1rem;
  font-weight: 600;
}

/* Modal Improvements */
.modal-content {
  border-radius: 15px;
  border: none;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.modal-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 15px 15px 0 0;
  border: none;
}

.modal-footer {
  border: none;
  border-radius: 0 0 15px 15px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .container {
    padding: 0 1rem;
  }

  .card-body {
    padding: 1.5rem;
  }

  .btn {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
  }

  .navbar-brand {
    font-size: 1.25rem;
  }

  .display-4 {
    font-size: 2rem;
  }

  .d-flex.gap-2 {
    flex-direction: column;
  }

  .d-flex.gap-2 .btn {
    margin-bottom: 0.5rem;
  }
}
