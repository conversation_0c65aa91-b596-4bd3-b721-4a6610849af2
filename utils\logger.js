const config = require('../config/config');

// Simple logger utility
const logger = {
  levels: {
    error: 0,
    warn: 1,
    info: 2,
    debug: 3
  },
  
  // Get current log level from config
  getLevel() {
    return this.levels[config.logging.level] || this.levels.info;
  },
  
  // Format log message
  formatMessage(level, message, meta = {}) {
    const timestamp = new Date().toISOString();
    const formattedMeta = meta ? JSON.stringify(meta) : '';
    return `[${timestamp}] [${level.toUpperCase()}] ${message} ${formattedMeta}`;
  },
  
  // Log methods
  error(message, meta) {
    if (this.getLevel() >= this.levels.error) {
      console.error(this.formatMessage('error', message, meta));
    }
  },
  
  warn(message, meta) {
    if (this.getLevel() >= this.levels.warn) {
      console.warn(this.formatMessage('warn', message, meta));
    }
  },
  
  info(message, meta) {
    if (this.getLevel() >= this.levels.info) {
      console.info(this.formatMessage('info', message, meta));
    }
  },
  
  debug(message, meta) {
    if (this.getLevel() >= this.levels.debug) {
      console.debug(this.formatMessage('debug', message, meta));
    }
  },
  
  // HTTP request logger
  request(req, res, next) {
    const startTime = new Date();
    
    // Log when the response is finished
    res.on('finish', () => {
      const duration = new Date() - startTime;
      const logLevel = res.statusCode >= 400 ? 'error' : 'info';
      
      const logData = {
        method: req.method,
        url: req.originalUrl,
        status: res.statusCode,
        duration: `${duration}ms`,
        ip: req.ip || req.headers['x-forwarded-for'] || req.connection.remoteAddress,
        userAgent: req.headers['user-agent']
      };
      
      this[logLevel](`HTTP ${req.method} ${req.originalUrl}`, logData);
    });
    
    next();
  }
};

module.exports = logger;
