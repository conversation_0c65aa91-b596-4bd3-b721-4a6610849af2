const Comment = require('../models/Comment');
const Post = require('../models/Post');

// @desc    Get comments for a post
// @route   GET /api/posts/:id/comments
// @access  Public
exports.getComments = async (req, res, next) => {
  try {
    const post = await Post.findById(req.params.id);

    if (!post) {
      return res.status(404).json({
        success: false,
        error: 'Post not found'
      });
    }

    const comments = await Comment.find({ postId: req.params.id })
      .sort('-createdAt')
      .populate('userId', 'username');

    res.status(200).json({
      success: true,
      count: comments.length,
      data: comments
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Add comment to post
// @route   POST /api/posts/:id/comments
// @access  Private
exports.createComment = async (req, res, next) => {
  try {
    const post = await Post.findById(req.params.id);

    if (!post) {
      return res.status(404).json({
        success: false,
        error: 'Post not found'
      });
    }

    const comment = await Comment.create({
      content: req.body.content,
      userId: req.user.id,
      postId: req.params.id
    });

    res.status(201).json({
      success: true,
      data: {
        id: comment._id,
        content: comment.content,
        userId: comment.userId,
        postId: comment.postId,
        createdAt: comment.createdAt
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update comment
// @route   PUT /api/posts/:id/comments/:commentId
// @access  Private (owner only)
exports.updateComment = async (req, res, next) => {
  try {
    let comment = await Comment.findById(req.params.commentId);

    if (!comment) {
      return res.status(404).json({
        success: false,
        error: 'Comment not found'
      });
    }

    // Check if comment belongs to the specified post
    if (comment.postId.toString() !== req.params.id) {
      return res.status(400).json({
        success: false,
        error: 'Comment does not belong to this post'
      });
    }

    comment = await Comment.findByIdAndUpdate(
      req.params.commentId,
      { content: req.body.content },
      {
        new: true,
        runValidators: true
      }
    );

    res.status(200).json({
      success: true,
      data: comment
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Delete comment
// @route   DELETE /api/posts/:id/comments/:commentId
// @access  Private (owner only)
exports.deleteComment = async (req, res, next) => {
  try {
    const comment = await Comment.findById(req.params.commentId);

    if (!comment) {
      return res.status(404).json({
        success: false,
        error: 'Comment not found'
      });
    }

    // Check if comment belongs to the specified post
    if (comment.postId.toString() !== req.params.id) {
      return res.status(400).json({
        success: false,
        error: 'Comment does not belong to this post'
      });
    }

    await comment.deleteOne();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    next(err);
  }
};
