const express = require('express');
const {
  register,
  login,
  logout,
  getUser,
  getUserProfile,
  refreshToken
} = require('../controllers/authController');
const { protect } = require('../middleware/auth');

const router = express.Router();

router.post('/register', register);
router.post('/login', login);
router.post('/logout', protect, logout);
router.get('/user', protect, getUser);
router.get('/user/:id/profile', getUserProfile);
router.post('/refresh', refreshToken);

module.exports = router;

