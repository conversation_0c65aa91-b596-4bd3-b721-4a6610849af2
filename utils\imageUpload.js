const mongoose = require('mongoose');
const multer = require('multer');
const { GridFsStorage } = require('multer-gridfs-storage');
const Grid = require('gridfs-stream');
const path = require('path');
const crypto = require('crypto');
const config = require('../config/config');
const logger = require('./logger');

// Create mongo connection
const conn = mongoose.connection;
let gfs, gridfsBucket;

// Initialize GridFS stream
conn.once('open', () => {
  // Initialize stream
  gridfsBucket = new mongoose.mongo.GridFSBucket(conn.db, {
    bucketName: 'uploads'
  });

  // Keep the old gfs for backward compatibility with existing code
  gfs = Grid(conn.db, mongoose.mongo);
  gfs.collection('uploads');

  logger.info('GridFS initialized for image uploads');
});

// Create storage engine
const storage = new GridFsStorage({
  url: config.db.uri,
  options: { useNewUrlParser: true, useUnifiedTopology: true },
  file: (req, file) => {
    return new Promise((resolve, reject) => {
      // Generate a random filename
      crypto.randomBytes(16, (err, buf) => {
        if (err) {
          logger.error('Error generating filename for upload', { error: err.message });
          return reject(err);
        }

        const filename = buf.toString('hex') + path.extname(file.originalname);
        const fileInfo = {
          filename: filename,
          bucketName: 'uploads',
          metadata: {
            originalName: file.originalname,
            uploadedBy: req.user ? req.user.id : 'anonymous',
            uploadDate: new Date()
          }
        };

        resolve(fileInfo);
      });
    });
  }
});

// File filter function
const fileFilter = (req, file, cb) => {
  // Accept images only
  if (!file.originalname.match(/\.(jpg|jpeg|png|gif)$/)) {
    req.fileValidationError = 'Only image files are allowed!';
    return cb(new Error('Only image files are allowed!'), false);
  }
  cb(null, true);
};

// Initialize upload middleware
const upload = multer({
  storage,
  limits: {
    fileSize: 5 * 1024 * 1024 // 5MB max file size
  },
  fileFilter
});

// Get image by filename
const getImage = async (filename) => {
  try {
    const file = await gfs.files.findOne({ filename });

    if (!file || file.length === 0) {
      throw new Error('No file exists');
    }

    // Check if the file is an image
    if (!file.contentType.startsWith('image/')) {
      throw new Error('Not an image');
    }

    return {
      id: file._id,
      filename: file.filename,
      contentType: file.contentType,
      uploadDate: file.uploadDate,
      metadata: file.metadata,
      size: file.length
    };
  } catch (err) {
    logger.error('Error retrieving image', { error: err.message, filename });
    throw err;
  }
};

// Stream image by filename
const streamImage = async (filename, res) => {
  try {
    const file = await gfs.files.findOne({ filename });

    if (!file || file.length === 0) {
      return res.status(404).json({
        success: false,
        error: 'No file exists'
      });
    }

    // Check if image
    if (file.contentType.startsWith('image/')) {
      // Set the proper content type
      res.set('Content-Type', file.contentType);

      // Create read stream using GridFSBucket
      if (gridfsBucket) {
        // Use the newer GridFSBucket API
        const downloadStream = gridfsBucket.openDownloadStreamByName(filename);
        // Return the file stream
        return downloadStream.pipe(res);
      } else {
        // Fallback to the older API if GridFSBucket is not initialized
        const readstream = gfs.createReadStream({ filename });
        return readstream.pipe(res);
      }
    } else {
      return res.status(404).json({
        success: false,
        error: 'Not an image'
      });
    }
  } catch (err) {
    logger.error('Error streaming image', { error: err.message, filename });
    return res.status(500).json({
      success: false,
      error: 'Server error'
    });
  }
};

// Delete image by filename
const deleteImage = async (filename) => {
  try {
    // Find the file first to get its ID
    const file = await gfs.files.findOne({ filename });

    if (!file) {
      logger.warn(`File not found for deletion: ${filename}`);
      return false;
    }

    if (gridfsBucket) {
      // Use the newer GridFSBucket API
      await gridfsBucket.delete(file._id);
    } else {
      // Fallback to the older API
      await gfs.files.deleteOne({ filename });
      await gfs.chunks.deleteMany({ files_id: file._id });
    }

    logger.info(`Image deleted: ${filename}`);
    return true;
  } catch (err) {
    logger.error('Error deleting image', { error: err.message, filename });
    throw err;
  }
};

module.exports = {
  upload,
  getImage,
  streamImage,
  deleteImage
};
