const User = require('../models/User');
const Post = require('../models/Post');
const Comment = require('../models/Comment');
const { deleteImage, streamImage } = require('../utils/imageUpload');

// @desc    Get account details
// @route   GET /api/account
// @access  Private
exports.getAccount = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);

    res.status(200).json({
      success: true,
      data: {
        id: user._id,
        username: user.username,
        email: user.email,
        profilePictureUrl: user.profilePicture ? `/api/account/profile-picture/${user.profilePicture}` : null,
        createdAt: user.createdAt
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update account details
// @route   PUT /api/account
// @access  Private
exports.updateAccount = async (req, res, next) => {
  try {
    const fieldsToUpdate = {
      username: req.body.username,
      email: req.body.email
    };

    // Remove undefined fields
    Object.keys(fieldsToUpdate).forEach(
      key => fieldsToUpdate[key] === undefined && delete fieldsToUpdate[key]
    );

    const user = await User.findByIdAndUpdate(req.user.id, fieldsToUpdate, {
      new: true,
      runValidators: true
    });

    res.status(200).json({
      success: true,
      data: {
        id: user._id,
        username: user.username,
        email: user.email,
        profilePictureUrl: user.profilePicture ? `/api/account/profile-picture/${user.profilePicture}` : null,
        createdAt: user.createdAt
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Delete account
// @route   DELETE /api/account
// @access  Private
exports.deleteAccount = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);

    // Delete profile picture if exists
    if (user.profilePicture) {
      try {
        await deleteImage(user.profilePicture);
      } catch (err) {
        console.log('Error deleting profile picture:', err.message);
      }
    }

    // Delete all user's posts
    await Post.deleteMany({ userId: req.user.id });

    // Delete all user's comments
    await Comment.deleteMany({ userId: req.user.id });

    // Delete user
    await User.findByIdAndDelete(req.user.id);

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Upload profile picture
// @route   POST /api/account/profile-picture
// @access  Private
exports.uploadProfilePicture = async (req, res, next) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'Please upload a file'
      });
    }

    const user = await User.findById(req.user.id);

    // Delete old profile picture if exists
    if (user.profilePicture) {
      try {
        await deleteImage(user.profilePicture);
      } catch (err) {
        console.log('Error deleting old profile picture:', err.message);
      }
    }

    // Update user with new profile picture filename
    user.profilePicture = req.file.filename;
    await user.save();

    res.status(200).json({
      success: true,
      data: {
        id: user._id,
        username: user.username,
        email: user.email,
        profilePictureUrl: `/api/account/profile-picture/${user.profilePicture}`,
        createdAt: user.createdAt
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get profile picture
// @route   GET /api/account/profile-picture/:filename
// @access  Public
exports.getProfilePicture = async (req, res, next) => {
  try {
    await streamImage(req.params.filename, res);
  } catch (err) {
    next(err);
  }
};

// @desc    Delete profile picture
// @route   DELETE /api/account/profile-picture
// @access  Private
exports.deleteProfilePicture = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);

    if (!user.profilePicture) {
      return res.status(400).json({
        success: false,
        error: 'No profile picture to delete'
      });
    }

    // Delete the image file
    try {
      await deleteImage(user.profilePicture);
    } catch (err) {
      console.log('Error deleting profile picture file:', err.message);
    }

    // Remove profile picture from user
    user.profilePicture = null;
    await user.save();

    res.status(200).json({
      success: true,
      data: {
        id: user._id,
        username: user.username,
        email: user.email,
        profilePictureUrl: null,
        createdAt: user.createdAt
      }
    });
  } catch (err) {
    next(err);
  }
};
