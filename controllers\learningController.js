const Learning = require('../models/Learning');
const UserProgress = require('../models/UserProgress');
const { saveImage, deleteImage } = require('../utils/imageUpload');
const logger = require('../utils/logger');

// @desc    Get all learning content
// @route   GET /api/learning
// @access  Public
exports.getAllLearning = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      sort = '-createdAt',
      category,
      difficulty,
      search,
      published = 'true'
    } = req.query;

    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const skip = (pageNum - 1) * limitNum;

    const filter = {};

    if (published === 'true') {
      filter.isPublished = true;
    }

    if (category) {
      filter.categories = category;
    }

    if (difficulty) {
      filter.difficulty = difficulty;
    }

    if (search) {
      filter.$text = { $search: search };
    }

    const learning = await Learning.find(filter)
      .sort(sort)
      .skip(skip)
      .limit(limitNum)
      .populate('userId', 'username')
      .populate('categories', 'name color');

    const total = await Learning.countDocuments(filter);

    res.status(200).json({
      success: true,
      count: learning.length,
      pagination: {
        total,
        page: pageNum,
        limit: limitNum,
        pages: Math.ceil(total / limitNum)
      },
      data: learning
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get single learning content
// @route   GET /api/learning/:id
// @access  Public
exports.getLearning = async (req, res, next) => {
  try {
    const learning = await Learning.findById(req.params.id)
      .populate('userId', 'username')
      .populate('categories', 'name color description');

    if (!learning) {
      return res.status(404).json({
        success: false,
        error: 'Learning content not found'
      });
    }

    await Learning.findByIdAndUpdate(req.params.id, {
      $inc: { viewCount: 1 }
    });

    let userProgress = null;
    if (req.user) {
      userProgress = await UserProgress.findOne({
        userId: req.user.id,
        learningId: req.params.id
      });
    }

    res.status(200).json({
      success: true,
      data: {
        ...learning.toJSON(),
        userProgress
      }
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Create new learning content
// @route   POST /api/learning
// @access  Private
exports.createLearning = async (req, res, next) => {
  try {
    req.body.userId = req.user.id;

    const learningData = {
      title: req.body.title,
      content: req.body.content,
      summary: req.body.summary,
      difficulty: req.body.difficulty,
      estimatedTime: req.body.estimatedTime,
      categories: req.body.categories ? JSON.parse(req.body.categories) : [],
      userId: req.user.id,
      isPublished: req.body.isPublished !== undefined ? req.body.isPublished : true
    };

    if (req.file) {
      learningData.image = {
        filename: req.file.filename,
        originalName: req.file.originalname,
        contentType: req.file.contentType || req.file.mimetype,
        size: req.file.size
      };

      logger.info(`Image uploaded for learning content: ${req.file.filename}`);
    }

    const learning = await Learning.create(learningData);

    await learning.populate('userId', 'username');
    await learning.populate('categories', 'name color');

    res.status(201).json({
      success: true,
      data: learning
    });
  } catch (err) {
    if (req.file && req.file.filename) {
      try {
        await deleteImage(req.file.filename);
        logger.info(`Deleted image ${req.file.filename} due to learning creation error`);
      } catch (deleteErr) {
        logger.error('Error deleting image after learning creation failure', {
          error: deleteErr.message,
          filename: req.file.filename
        });
      }
    }
    next(err);
  }
};

// @desc    Update learning content
// @route   PUT /api/learning/:id
// @access  Private (owner only)
exports.updateLearning = async (req, res, next) => {
  try {
    let learning = await Learning.findById(req.params.id);

    if (!learning) {
      return res.status(404).json({
        success: false,
        error: 'Learning content not found'
      });
    }

    const updateData = {
      title: req.body.title,
      content: req.body.content,
      summary: req.body.summary,
      difficulty: req.body.difficulty,
      estimatedTime: req.body.estimatedTime,
      isPublished: req.body.isPublished
    };

    if (req.body.categories) {
      updateData.categories = JSON.parse(req.body.categories);
    }

    if (req.file) {
      if (learning.image && learning.image.filename) {
        try {
          await deleteImage(learning.image.filename);
          logger.info(`Deleted old image ${learning.image.filename} during learning update`);
        } catch (deleteErr) {
          logger.error('Error deleting old image during learning update', {
            error: deleteErr.message,
            filename: learning.image.filename
          });
        }
      }

      updateData.image = {
        filename: req.file.filename,
        originalName: req.file.originalname,
        contentType: req.file.contentType || req.file.mimetype,
        size: req.file.size
      };

      logger.info(`New image uploaded for learning content: ${req.file.filename}`);
    }

    learning = await Learning.findByIdAndUpdate(req.params.id, updateData, {
      new: true,
      runValidators: true
    }).populate('userId', 'username').populate('categories', 'name color');

    res.status(200).json({
      success: true,
      data: learning
    });
  } catch (err) {
    if (req.file && req.file.filename) {
      try {
        await deleteImage(req.file.filename);
        logger.info(`Deleted image ${req.file.filename} due to learning update error`);
      } catch (deleteErr) {
        logger.error('Error deleting image after learning update failure', {
          error: deleteErr.message,
          filename: req.file.filename
        });
      }
    }
    next(err);
  }
};

// @desc    Delete learning content
// @route   DELETE /api/learning/:id
// @access  Private (owner only)
exports.deleteLearning = async (req, res, next) => {
  try {
    const learning = await Learning.findById(req.params.id);

    if (!learning) {
      return res.status(404).json({
        success: false,
        error: 'Learning content not found'
      });
    }

    if (learning.image && learning.image.filename) {
      try {
        await deleteImage(learning.image.filename);
        logger.info(`Deleted image ${learning.image.filename} during learning deletion`);
      } catch (deleteErr) {
        logger.error('Error deleting image during learning deletion', {
          error: deleteErr.message,
          filename: learning.image.filename
        });
      }
    }

    await UserProgress.deleteMany({ learningId: req.params.id });
    await learning.deleteOne();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get learning image
// @route   GET /api/learning/image/:filename
// @access  Public
exports.getLearningImage = async (req, res, next) => {
  try {
    const { filename } = req.params;

    if (!filename) {
      return res.status(400).json({
        success: false,
        error: 'Filename is required'
      });
    }

    const fs = require('fs');
    const path = require('path');
    const uploadsDir = path.join(__dirname, '..', 'uploads');
    const imagePath = path.join(uploadsDir, filename);

    if (!fs.existsSync(imagePath)) {
      return res.status(404).json({
        success: false,
        error: 'Image not found'
      });
    }

    const stat = fs.statSync(imagePath);
    const fileSize = stat.size;
    const range = req.headers.range;

    if (range) {
      const parts = range.replace(/bytes=/, "").split("-");
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
      const chunksize = (end - start) + 1;
      const file = fs.createReadStream(imagePath, { start, end });
      const head = {
        'Content-Range': `bytes ${start}-${end}/${fileSize}`,
        'Accept-Ranges': 'bytes',
        'Content-Length': chunksize,
        'Content-Type': 'image/jpeg',
      };
      res.writeHead(206, head);
      file.pipe(res);
    } else {
      const head = {
        'Content-Length': fileSize,
        'Content-Type': 'image/jpeg',
      };
      res.writeHead(200, head);
      fs.createReadStream(imagePath).pipe(res);
    }
  } catch (err) {
    next(err);
  }
};

// @desc    Get learning content by category
// @route   GET /api/learning/category/:categoryId
// @access  Public
exports.getLearningByCategory = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, sort = '-createdAt' } = req.query;
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const skip = (pageNum - 1) * limitNum;

    const learning = await Learning.find({
      categories: req.params.categoryId,
      isPublished: true
    })
      .sort(sort)
      .skip(skip)
      .limit(limitNum)
      .populate('userId', 'username')
      .populate('categories', 'name color');

    const total = await Learning.countDocuments({
      categories: req.params.categoryId,
      isPublished: true
    });

    res.status(200).json({
      success: true,
      count: learning.length,
      pagination: {
        total,
        page: pageNum,
        limit: limitNum,
        pages: Math.ceil(total / limitNum)
      },
      data: learning
    });
  } catch (err) {
    next(err);
  }
};