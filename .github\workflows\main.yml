name: Node.js API CI/CD

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  build:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [16.x, 18.x]

    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v3
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm install
      
    - name: Run linting
      run: npm run lint
      
    - name: Run tests
      run: npm test
      env:
        NODE_ENV: test
        MONGODB_URI: ${{ secrets.MONGODB_URI_TEST }}
        JWT_SECRET: ${{ secrets.JWT_SECRET_TEST }}
        JWT_EXPIRE: 1h
        JWT_REFRESH_SECRET: ${{ secrets.JWT_REFRESH_SECRET_TEST }}
        JWT_REFRESH_EXPIRE: 1d

  deploy:
    needs: build
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/master')
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Use Node.js 16.x
      uses: actions/setup-node@v3
      with:
        node-version: 16.x
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    # Example deployment to Heroku
    # Uncomment and configure as needed
    # - name: Deploy to Heroku
    #   uses: akhileshns/heroku-deploy@v3.12.12
    #   with:
    #     heroku_api_key: ${{ secrets.HEROKU_API_KEY }}
    #     heroku_app_name: ${{ secrets.HEROKU_APP_NAME }}
    #     heroku_email: ${{ secrets.HEROKU_EMAIL }}
    
    # Example deployment to Digital Ocean
    # Uncomment and configure as needed
    # - name: Install doctl
    #   uses: digitalocean/action-doctl@v2
    #   with:
    #     token: ${{ secrets.DIGITALOCEAN_ACCESS_TOKEN }}
    # - name: Deploy to Digital Ocean
    #   run: |
    #     doctl apps create-deployment ${{ secrets.DIGITALOCEAN_APP_ID }}
    
    # Example deployment to AWS Elastic Beanstalk
    # Uncomment and configure as needed
    # - name: Generate deployment package
    #   run: zip -r deploy.zip . -x "node_modules/*" ".git/*"
    # - name: Deploy to AWS Elastic Beanstalk
    #   uses: einaregilsson/beanstalk-deploy@v21
    #   with:
    #     aws_access_key: ${{ secrets.AWS_ACCESS_KEY_ID }}
    #     aws_secret_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
    #     application_name: ${{ secrets.EB_APPLICATION_NAME }}
    #     environment_name: ${{ secrets.EB_ENVIRONMENT_NAME }}
    #     version_label: ${{ github.sha }}
    #     region: ${{ secrets.AWS_REGION }}
    #     deployment_package: deploy.zip
    
    - name: Notify deployment status
      run: |
        echo "Deployment completed successfully!"
        # Add notification logic here (e.g., Slack, Discord, email)

