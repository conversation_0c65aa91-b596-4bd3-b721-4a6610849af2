# Server Configuration
PORT=5000
NODE_ENV=development

# MongoDB Configuration
MONGODB_URI=mongodb+srv://<username>:<password>@<cluster>.mongodb.net/<database>?retryWrites=true&w=majority

# JWT Configuration
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRE=30d
JWT_REFRESH_SECRET=your_jwt_refresh_secret_key
JWT_REFRESH_EXPIRE=7d

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000  # 15 minutes in milliseconds
RATE_LIMIT_MAX=100

# Logging
LOG_LEVEL=info  # debug, info, warn, error

# CORS Configuration
CORS_ORIGIN=*  # Use specific origins in production, e.g., https://yourdomain.com

# Security
HELMET_CONTENT_SECURITY_POLICY=false  # Set to true in production
