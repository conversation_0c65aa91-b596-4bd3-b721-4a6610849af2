const mongoose = require('mongoose');

const UserProgressSchema = new mongoose.Schema({
  userId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  learningId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Learning',
    required: true
  },
  isBookmarked: {
    type: Boolean,
    default: false
  },
  isCompleted: {
    type: Boolean,
    default: false
  },
  readingProgress: {
    type: Number,
    default: 0,
    min: 0,
    max: 100
  },
  completedAt: {
    type: Date,
    default: null
  },
  bookmarkedAt: {
    type: Date,
    default: null
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
});

UserProgressSchema.pre('save', function(next) {
  this.updatedAt = Date.now();
  
  if (this.isCompleted && !this.completedAt) {
    this.completedAt = Date.now();
    this.readingProgress = 100;
  }
  
  if (this.isBookmarked && !this.bookmarkedAt) {
    this.bookmarkedAt = Date.now();
  } else if (!this.isBookmarked) {
    this.bookmarkedAt = null;
  }
  
  next();
});

UserProgressSchema.index({ userId: 1, learningId: 1 }, { unique: true });
UserProgressSchema.index({ userId: 1, isBookmarked: 1 });
UserProgressSchema.index({ userId: 1, isCompleted: 1 });

UserProgressSchema.set('toJSON', {
  transform: function(doc, ret) {
    if (ret._id) {
      ret.id = ret._id.toString();
      delete ret._id;
    }
    delete ret.__v;
    return ret;
  }
});

module.exports = mongoose.model('UserProgress', UserProgressSchema);
