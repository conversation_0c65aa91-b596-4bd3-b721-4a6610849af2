<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>API Documentation</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
  <link rel="stylesheet" href="css/style.css">
  <style>
    /* Enhanced Styling for API Documentation */
    .endpoint {
      background: rgba(255, 255, 255, 0.95);
      border-radius: 15px;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      border-left: 4px solid #667eea;
      transition: all 0.3s ease;
      cursor: pointer;
    }

    .endpoint:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    .endpoint-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 1rem;
    }

    .method {
      font-weight: 700;
      display: inline-block;
      padding: 8px 16px;
      border-radius: 25px;
      color: white;
      margin-right: 15px;
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    }

    .method.get {
      background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    }
    .method.post {
      background: linear-gradient(135deg, #007bff 0%, #6610f2 100%);
    }
    .method.put {
      background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
      color: #212529;
    }
    .method.delete {
      background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    }

    .endpoint-url {
      font-family: 'Courier New', monospace;
      font-size: 1.1rem;
      font-weight: 600;
      color: #495057;
      background: #f8f9fa;
      padding: 8px 12px;
      border-radius: 8px;
      flex: 1;
    }

    .endpoint-toggle {
      background: none;
      border: none;
      color: #6c757d;
      font-size: 1.2rem;
      transition: all 0.3s ease;
    }

    .endpoint-toggle:hover {
      color: #667eea;
      transform: scale(1.1);
    }

    .endpoint-content {
      display: none;
      margin-top: 1rem;
      padding-top: 1rem;
      border-top: 1px solid #e9ecef;
    }

    .endpoint-content.show {
      display: block;
      animation: fadeInUp 0.3s ease;
    }

    /* Ensure endpoint content in inactive tabs is completely hidden */
    .tab-pane:not(.active) .endpoint-content {
      display: none !important;
    }

    .tab-pane:not(.active) .endpoint-content.show {
      display: none !important;
    }

    .source-code-section {
      background: #2d3748;
      border-radius: 10px;
      padding: 1rem;
      margin-top: 1rem;
    }

    .source-code-header {
      display: flex;
      align-items: center;
      justify-content: between;
      margin-bottom: 1rem;
      color: #e2e8f0;
    }

    .copy-btn {
      background: #4a5568;
      border: none;
      color: #e2e8f0;
      padding: 0.5rem 1rem;
      border-radius: 5px;
      font-size: 0.8rem;
      transition: all 0.3s ease;
    }

    .copy-btn:hover {
      background: #667eea;
      color: white;
    }

    .code-block {
      background: #1a202c;
      border-radius: 8px;
      padding: 1rem;
      overflow-x: auto;
      margin: 0.5rem 0;
    }

    .code-block code {
      color: #e2e8f0;
      font-family: 'Courier New', monospace;
      font-size: 0.9rem;
      line-height: 1.5;
    }

    .tab-content {
      background: transparent;
      border: none;
      padding: 0;
    }

    .nav-tabs .nav-link {
      border: none;
      border-radius: 10px 10px 0 0;
      color: #6c757d;
      font-weight: 600;
      padding: 1rem 1.5rem;
      transition: all 0.3s ease;
      margin-right: 0.5rem;
    }

    .nav-tabs .nav-link:hover {
      background: rgba(102, 126, 234, 0.1);
      color: #667eea;
    }

    .nav-tabs .nav-link.active {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      border: none;
    }

    .endpoint-description {
      color: #6c757d;
      margin-bottom: 1rem;
      font-style: italic;
    }

    .parameter-list {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 1rem;
      margin: 1rem 0;
    }

    .parameter-item {
      display: flex;
      align-items: center;
      margin-bottom: 0.5rem;
    }

    .parameter-name {
      font-family: monospace;
      font-weight: 600;
      color: #495057;
      margin-right: 1rem;
      min-width: 120px;
    }

    .parameter-type {
      background: #e9ecef;
      color: #495057;
      padding: 2px 8px;
      border-radius: 4px;
      font-size: 0.8rem;
      margin-right: 1rem;
    }

    .parameter-description {
      color: #6c757d;
      flex: 1;
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .status-badge {
      display: inline-block;
      padding: 4px 8px;
      border-radius: 4px;
      font-size: 0.8rem;
      font-weight: 600;
      margin-left: 0.5rem;
    }

    .status-auth {
      background: #fff3cd;
      color: #856404;
    }

    .status-owner {
      background: #f8d7da;
      color: #721c24;
    }

    .status-public {
      background: #d1ecf1;
      color: #0c5460;
    }
  </style>
</head>
<body>
  <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
    <div class="container">
      <a class="navbar-brand" href="/">API Project</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav">
          <li class="nav-item">
            <a class="nav-link" href="/">Test Page</a>
          </li>
          <li class="nav-item">
            <a class="nav-link active" href="/api-docs.html">API Documentation</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/docs.html">Deployment Guide</a>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <div class="container mt-4">
    <div class="text-center mb-5">
      <h1 class="display-4 text-white fw-bold mb-3">📚 API Documentation</h1>
      <p class="lead text-white-50">Dokumentasi lengkap untuk API REST yang dibangun dengan Node.js, Express, dan MongoDB</p>
    </div>

    <ul class="nav nav-tabs" id="apiTabs" role="tablist">
      <li class="nav-item" role="presentation">
        <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview" type="button" role="tab">
          <i class="fas fa-home me-2"></i>Overview
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="auth-tab" data-bs-toggle="tab" data-bs-target="#auth" type="button" role="tab">
          <i class="fas fa-lock me-2"></i>Authentication
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="posts-tab" data-bs-toggle="tab" data-bs-target="#posts" type="button" role="tab">
          <i class="fas fa-file-alt me-2"></i>Posts
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="comments-tab" data-bs-toggle="tab" data-bs-target="#comments" type="button" role="tab">
          <i class="fas fa-comments me-2"></i>Comments
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="account-tab" data-bs-toggle="tab" data-bs-target="#account" type="button" role="tab">
          <i class="fas fa-user me-2"></i>Account
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="categories-tab" data-bs-toggle="tab" data-bs-target="#categories" type="button" role="tab">
          <i class="fas fa-tags me-2"></i>Categories
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="learning-tab" data-bs-toggle="tab" data-bs-target="#learning" type="button" role="tab">
          <i class="fas fa-graduation-cap me-2"></i>Learning
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="progress-tab" data-bs-toggle="tab" data-bs-target="#progress" type="button" role="tab">
          <i class="fas fa-chart-line me-2"></i>Progress
        </button>
      </li>
      <li class="nav-item" role="presentation">
        <button class="nav-link" id="errors-tab" data-bs-toggle="tab" data-bs-target="#errors" type="button" role="tab">
          <i class="fas fa-exclamation-triangle me-2"></i>Errors
        </button>
      </li>
    </ul>

    <div class="tab-content" id="apiTabsContent">
      <!-- Overview Tab -->
      <div class="tab-pane fade show active" id="overview" role="tabpanel">
        <div class="card">
          <div class="card-header">
            <h2 class="h5 mb-0"><i class="fas fa-info-circle me-2"></i>API Overview</h2>
          </div>
          <div class="card-body">
            <p class="lead">API ini menyediakan endpoint untuk manajemen pengguna, post, komentar, dan sistem pembelajaran. Semua respons dikembalikan dalam format JSON.</p>

            <div class="row">
              <div class="col-md-6">
                <h4><i class="fas fa-link me-2"></i>Base URL</h4>
                <div class="code-block">
                  <code>http://localhost:5000/api</code>
                </div>

                <h4 class="mt-4"><i class="fas fa-shield-alt me-2"></i>Authentication</h4>
                <p>Sebagian besar endpoint memerlukan autentikasi. Untuk mengakses endpoint yang dilindungi, sertakan token JWT di header Authorization:</p>
                <div class="code-block">
                  <code>Authorization: Bearer &lt;your_token&gt;</code>
                </div>
              </div>
              <div class="col-md-6">
                <h4><i class="fas fa-code me-2"></i>Response Format</h4>
                <p>Semua respons mengikuti format yang konsisten:</p>
                <div class="code-block">
                  <code>{
  "success": true|false,
  "data": { ... } | [ ... ],  // Untuk respons sukses
  "error": "Error message",   // Untuk respons error
  "count": 10,                // Untuk respons dengan banyak item
  "pagination": { ... }       // Untuk respons dengan pagination
}</code>
                </div>
              </div>
            </div>

            <div class="mt-4">
              <h4><i class="fas fa-list me-2"></i>Available Endpoints</h4>
              <div class="row">
                <div class="col-md-4">
                  <div class="card h-100">
                    <div class="card-body text-center">
                      <i class="fas fa-lock fa-2x text-primary mb-3"></i>
                      <h6>Authentication</h6>
                      <p class="small text-muted">Register, login, logout, dan manajemen token</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="card h-100">
                    <div class="card-body text-center">
                      <i class="fas fa-file-alt fa-2x text-success mb-3"></i>
                      <h6>Posts & Comments</h6>
                      <p class="small text-muted">CRUD operations untuk posts dan komentar</p>
                    </div>
                  </div>
                </div>
                <div class="col-md-4">
                  <div class="card h-100">
                    <div class="card-body text-center">
                      <i class="fas fa-graduation-cap fa-2x text-info mb-3"></i>
                      <h6>Learning System</h6>
                      <p class="small text-muted">Konten pembelajaran, kategori, dan progress tracking</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Authentication Tab -->
      <div class="tab-pane fade" id="auth" role="tabpanel">
        <!-- Register Endpoint -->
        <div class="endpoint" onclick="toggleEndpoint('auth-register')">
          <div class="endpoint-header">
            <div class="d-flex align-items-center">
              <span class="method post">POST</span>
              <span class="endpoint-url">/api/auth/register</span>
              <span class="status-badge status-public">Public</span>
            </div>
            <button class="endpoint-toggle" type="button">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          <div class="endpoint-description">
            Mendaftarkan pengguna baru ke dalam sistem
          </div>
          <div class="endpoint-content" id="auth-register">
            <div class="parameter-list">
              <h6><i class="fas fa-arrow-down me-2"></i>Request Body</h6>
              <div class="parameter-item">
                <span class="parameter-name">username</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Username unik untuk pengguna (required)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">email</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Email address yang valid (required)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">password</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Password minimal 6 karakter (required)</span>
              </div>
            </div>

            <h6><i class="fas fa-arrow-up me-2"></i>Response Example</h6>
            <div class="code-block">
              <code>{
  "success": true,
  "user": {
    "id": "507f1f77bcf86cd799439011",
    "username": "johndoe",
    "email": "<EMAIL>",
    "profilePictureUrl": null,
    "createdAt": "2023-12-01T10:00:00.000Z"
  },
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}</code>
            </div>

            <div class="source-code-section">
              <div class="source-code-header">
                <h6><i class="fas fa-code me-2"></i>Source Code</h6>
                <button class="copy-btn" onclick="copyCode('auth-register-code')">
                  <i class="fas fa-copy me-1"></i>Copy
                </button>
              </div>
              <div class="code-block" id="auth-register-code">
                <code>// controllers/authController.js
exports.register = async (req, res, next) => {
  try {
    const { username, email, password } = req.body;

    // Create user
    const user = await User.create({
      username,
      email,
      password
    });

    // Generate token
    const token = user.getSignedJwtToken();

    res.status(201).json({
      success: true,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        profilePictureUrl: user.profilePicture ?
          `/api/account/profile-picture/${user.profilePicture}` : null,
        createdAt: user.createdAt
      },
      token
    });
  } catch (err) {
    next(err);
  }
};</code>
              </div>
            </div>
          </div>
        </div>

        <!-- Login Endpoint -->
        <div class="endpoint" onclick="toggleEndpoint('auth-login')">
          <div class="endpoint-header">
            <div class="d-flex align-items-center">
              <span class="method post">POST</span>
              <span class="endpoint-url">/api/auth/login</span>
              <span class="status-badge status-public">Public</span>
            </div>
            <button class="endpoint-toggle" type="button">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          <div class="endpoint-description">
            Login pengguna dengan email dan password
          </div>
          <div class="endpoint-content" id="auth-login">
            <div class="parameter-list">
              <h6><i class="fas fa-arrow-down me-2"></i>Request Body</h6>
              <div class="parameter-item">
                <span class="parameter-name">email</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Email address pengguna (required)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">password</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Password pengguna (required)</span>
              </div>
            </div>

            <h6><i class="fas fa-arrow-up me-2"></i>Response Example</h6>
            <div class="code-block">
              <code>{
  "success": true,
  "user": {
    "id": "507f1f77bcf86cd799439011",
    "username": "johndoe",
    "email": "<EMAIL>",
    "profilePictureUrl": null
  },
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}</code>
            </div>

            <div class="source-code-section">
              <div class="source-code-header">
                <h6><i class="fas fa-code me-2"></i>Source Code</h6>
                <button class="copy-btn" onclick="copyCode('auth-login-code')">
                  <i class="fas fa-copy me-1"></i>Copy
                </button>
              </div>
              <div class="code-block" id="auth-login-code">
                <code>// controllers/authController.js
exports.login = async (req, res, next) => {
  try {
    const { email, password } = req.body;

    // Validate email & password
    if (!email || !password) {
      return res.status(400).json({
        success: false,
        error: 'Please provide an email and password'
      });
    }

    // Check for user
    const user = await User.findOne({ email }).select('+password');

    if (!user) {
      return res.status(401).json({
        success: false,
        error: 'Invalid credentials'
      });
    }

    // Check if password matches
    const isMatch = await user.matchPassword(password);

    if (!isMatch) {
      return res.status(401).json({
        success: false,
        error: 'Invalid credentials'
      });
    }

    // Generate token
    const token = user.getSignedJwtToken();

    res.status(200).json({
      success: true,
      user: {
        id: user._id,
        username: user.username,
        email: user.email,
        profilePictureUrl: user.profilePicture ?
          `/api/account/profile-picture/${user.profilePicture}` : null
      },
      token
    });
  } catch (err) {
    next(err);
  }
};</code>
              </div>
            </div>
          </div>
        </div>

        <!-- Get User Endpoint -->
        <div class="endpoint" onclick="toggleEndpoint('auth-user')">
          <div class="endpoint-header">
            <div class="d-flex align-items-center">
              <span class="method get">GET</span>
              <span class="endpoint-url">/api/auth/user</span>
              <span class="status-badge status-auth">Auth Required</span>
            </div>
            <button class="endpoint-toggle" type="button">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          <div class="endpoint-description">
            Mendapatkan data pengguna yang sedang login
          </div>
          <div class="endpoint-content" id="auth-user">
            <div class="parameter-list">
              <h6><i class="fas fa-key me-2"></i>Headers</h6>
              <div class="parameter-item">
                <span class="parameter-name">Authorization</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Bearer token (required)</span>
              </div>
            </div>

            <h6><i class="fas fa-arrow-up me-2"></i>Response Example</h6>
            <div class="code-block">
              <code>{
  "success": true,
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "username": "johndoe",
    "email": "<EMAIL>",
    "profilePictureUrl": null,
    "createdAt": "2023-12-01T10:00:00.000Z"
  }
}</code>
            </div>

            <div class="source-code-section">
              <div class="source-code-header">
                <h6><i class="fas fa-code me-2"></i>Source Code</h6>
                <button class="copy-btn" onclick="copyCode('auth-user-code')">
                  <i class="fas fa-copy me-1"></i>Copy
                </button>
              </div>
              <div class="code-block" id="auth-user-code">
                <code>// controllers/authController.js
exports.getUser = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);

    res.status(200).json({
      success: true,
      data: {
        id: user._id,
        username: user.username,
        email: user.email,
        profilePictureUrl: user.profilePicture ?
          `/api/account/profile-picture/${user.profilePicture}` : null,
        createdAt: user.createdAt
      }
    });
  } catch (err) {
    next(err);
  }
};</code>
              </div>
            </div>
          </div>
        </div>

        <!-- Logout Endpoint -->
        <div class="endpoint" onclick="toggleEndpoint('auth-logout')">
          <div class="endpoint-header">
            <div class="d-flex align-items-center">
              <span class="method post">POST</span>
              <span class="endpoint-url">/api/auth/logout</span>
              <span class="status-badge status-auth">Auth Required</span>
            </div>
            <button class="endpoint-toggle" type="button">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          <div class="endpoint-description">
            Logout pengguna dari sistem
          </div>
          <div class="endpoint-content" id="auth-logout">
            <div class="parameter-list">
              <h6><i class="fas fa-key me-2"></i>Headers</h6>
              <div class="parameter-item">
                <span class="parameter-name">Authorization</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Bearer token (required)</span>
              </div>
            </div>

            <h6><i class="fas fa-arrow-up me-2"></i>Response Example</h6>
            <div class="code-block">
              <code>{
  "success": true,
  "data": {}
}</code>
            </div>

            <div class="source-code-section">
              <div class="source-code-header">
                <h6><i class="fas fa-code me-2"></i>Source Code</h6>
                <button class="copy-btn" onclick="copyCode('auth-logout-code')">
                  <i class="fas fa-copy me-1"></i>Copy
                </button>
              </div>
              <div class="code-block" id="auth-logout-code">
                <code>// controllers/authController.js
exports.logout = async (req, res, next) => {
  try {
    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    next(err);
  }
};</code>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Posts Tab -->
      <div class="tab-pane fade" id="posts" role="tabpanel">
        <!-- Get All Posts Endpoint -->
        <div class="endpoint" onclick="toggleEndpoint('posts-getall')">
          <div class="endpoint-header">
            <div class="d-flex align-items-center">
              <span class="method get">GET</span>
              <span class="endpoint-url">/api/posts</span>
              <span class="status-badge status-public">Public</span>
            </div>
            <button class="endpoint-toggle" type="button">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          <div class="endpoint-description">
            Mendapatkan semua posts dengan pagination
          </div>
          <div class="endpoint-content" id="posts-getall">
            <div class="parameter-list">
              <h6><i class="fas fa-search me-2"></i>Query Parameters (Optional)</h6>
              <div class="parameter-item">
                <span class="parameter-name">page</span>
                <span class="parameter-type">number</span>
                <span class="parameter-description">Nomor halaman (default: 1)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">limit</span>
                <span class="parameter-type">number</span>
                <span class="parameter-description">Jumlah posts per halaman (default: 10)</span>
              </div>
            </div>

            <h6><i class="fas fa-arrow-up me-2"></i>Response Example</h6>
            <div class="code-block">
              <code>{
  "success": true,
  "count": 25,
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "pages": 3
  },
  "data": [
    {
      "id": "507f1f77bcf86cd799439011",
      "title": "My First Post",
      "content": "This is the content of my first post...",
      "imageUrl": "/api/posts/image/507f1f77bcf86cd799439011",
      "author": {
        "id": "507f1f77bcf86cd799439012",
        "username": "johndoe"
      },
      "createdAt": "2023-12-01T10:00:00.000Z",
      "updatedAt": "2023-12-01T10:00:00.000Z"
    }
  ]
}</code>
            </div>

            <div class="source-code-section">
              <div class="source-code-header">
                <h6><i class="fas fa-code me-2"></i>Source Code</h6>
                <button class="copy-btn" onclick="copyCode('posts-getall-code')">
                  <i class="fas fa-copy me-1"></i>Copy
                </button>
              </div>
              <div class="code-block" id="posts-getall-code">
                <code>// controllers/postController.js
exports.getPosts = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    const total = await Post.countDocuments();
    const posts = await Post.find()
      .populate('author', 'username')
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(startIndex);

    const pagination = {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    };

    res.status(200).json({
      success: true,
      count: posts.length,
      pagination,
      data: posts
    });
  } catch (err) {
    next(err);
  }
};</code>
              </div>
            </div>
          </div>
        </div>

        <!-- Create Post Endpoint -->
        <div class="endpoint" onclick="toggleEndpoint('posts-create')">
          <div class="endpoint-header">
            <div class="d-flex align-items-center">
              <span class="method post">POST</span>
              <span class="endpoint-url">/api/posts</span>
              <span class="status-badge status-auth">Auth Required</span>
            </div>
            <button class="endpoint-toggle" type="button">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          <div class="endpoint-description">
            Membuat post baru dengan dukungan upload gambar
          </div>
          <div class="endpoint-content" id="posts-create">
            <div class="parameter-list">
              <h6><i class="fas fa-key me-2"></i>Headers</h6>
              <div class="parameter-item">
                <span class="parameter-name">Authorization</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Bearer token (required)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">Content-Type</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">multipart/form-data (for file upload)</span>
              </div>
            </div>

            <div class="parameter-list">
              <h6><i class="fas fa-arrow-down me-2"></i>Form Data</h6>
              <div class="parameter-item">
                <span class="parameter-name">title</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Judul post (required)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">content</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Konten post (required)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">image</span>
                <span class="parameter-type">file</span>
                <span class="parameter-description">File gambar (optional, max 5MB)</span>
              </div>
            </div>

            <h6><i class="fas fa-arrow-up me-2"></i>Response Example</h6>
            <div class="code-block">
              <code>{
  "success": true,
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "title": "My New Post",
    "content": "This is my new post content...",
    "imageUrl": "/api/posts/image/507f1f77bcf86cd799439011",
    "author": {
      "id": "507f1f77bcf86cd799439012",
      "username": "johndoe"
    },
    "createdAt": "2023-12-01T10:00:00.000Z",
    "updatedAt": "2023-12-01T10:00:00.000Z"
  }
}</code>
            </div>

            <div class="source-code-section">
              <div class="source-code-header">
                <h6><i class="fas fa-code me-2"></i>Source Code</h6>
                <button class="copy-btn" onclick="copyCode('posts-create-code')">
                  <i class="fas fa-copy me-1"></i>Copy
                </button>
              </div>
              <div class="code-block" id="posts-create-code">
                <code>// controllers/postController.js
exports.createPost = async (req, res, next) => {
  try {
    const { title, content } = req.body;

    const postData = {
      title,
      content,
      author: req.user.id
    };

    // Handle image upload if present
    if (req.file) {
      postData.image = req.file.filename;
    }

    const post = await Post.create(postData);
    await post.populate('author', 'username');

    res.status(201).json({
      success: true,
      data: post
    });
  } catch (err) {
    next(err);
  }
};</code>
              </div>
            </div>
          </div>
        </div>

        <!-- Delete Post Endpoint -->
        <div class="endpoint" onclick="toggleEndpoint('posts-delete')">
          <div class="endpoint-header">
            <div class="d-flex align-items-center">
              <span class="method delete">DELETE</span>
              <span class="endpoint-url">/api/posts/:id</span>
              <span class="status-badge status-owner">Owner Only</span>
            </div>
            <button class="endpoint-toggle" type="button">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          <div class="endpoint-description">
            Menghapus post (hanya pemilik post yang dapat menghapus)
          </div>
          <div class="endpoint-content" id="posts-delete">
            <div class="parameter-list">
              <h6><i class="fas fa-key me-2"></i>Headers</h6>
              <div class="parameter-item">
                <span class="parameter-name">Authorization</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Bearer token (required)</span>
              </div>
            </div>

            <div class="parameter-list">
              <h6><i class="fas fa-route me-2"></i>Path Parameters</h6>
              <div class="parameter-item">
                <span class="parameter-name">id</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">ID post yang akan dihapus (required)</span>
              </div>
            </div>

            <h6><i class="fas fa-arrow-up me-2"></i>Response Example</h6>
            <div class="code-block">
              <code>{
  "success": true,
  "data": {}
}</code>
            </div>

            <div class="source-code-section">
              <div class="source-code-header">
                <h6><i class="fas fa-code me-2"></i>Source Code</h6>
                <button class="copy-btn" onclick="copyCode('posts-delete-code')">
                  <i class="fas fa-copy me-1"></i>Copy
                </button>
              </div>
              <div class="code-block" id="posts-delete-code">
                <code>// controllers/postController.js
exports.deletePost = async (req, res, next) => {
  try {
    const post = await Post.findById(req.params.id);

    if (!post) {
      return res.status(404).json({
        success: false,
        error: 'Post not found'
      });
    }

    // Check if user is the owner
    if (post.author.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        error: 'Not authorized to delete this post'
      });
    }

    // Delete image file if exists
    if (post.image) {
      const imagePath = path.join(__dirname, '../uploads', post.image);
      if (fs.existsSync(imagePath)) {
        fs.unlinkSync(imagePath);
      }
    }

    await post.deleteOne();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    next(err);
  }
};</code>
              </div>
            </div>
          </div>
        </div>
      </div>

      </div>

      <!-- Comments Tab -->
      <div class="tab-pane fade" id="comments" role="tabpanel">
        <!-- Get Comments for Post -->
        <div class="endpoint" onclick="toggleEndpoint('comments-get')">
          <div class="endpoint-header">
            <div class="d-flex align-items-center">
              <span class="method get">GET</span>
              <span class="endpoint-url">/api/posts/:postId/comments</span>
              <span class="status-badge status-public">Public</span>
            </div>
            <button class="endpoint-toggle" type="button">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          <div class="endpoint-description">
            Mendapatkan semua komentar untuk post tertentu
          </div>
          <div class="endpoint-content" id="comments-get">
            <div class="parameter-list">
              <h6><i class="fas fa-route me-2"></i>Path Parameters</h6>
              <div class="parameter-item">
                <span class="parameter-name">postId</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">ID post yang akan diambil komentarnya (required)</span>
              </div>
            </div>

            <h6><i class="fas fa-arrow-up me-2"></i>Response Example</h6>
            <div class="code-block">
              <code>{
  "success": true,
  "count": 3,
  "data": [
    {
      "id": "507f1f77bcf86cd799439011",
      "content": "Great post! Very informative.",
      "author": {
        "id": "507f1f77bcf86cd799439012",
        "username": "johndoe"
      },
      "post": "507f1f77bcf86cd799439013",
      "createdAt": "2023-12-01T10:00:00.000Z",
      "updatedAt": "2023-12-01T10:00:00.000Z"
    }
  ]
}</code>
            </div>

            <div class="source-code-section">
              <div class="source-code-header">
                <h6><i class="fas fa-code me-2"></i>Source Code</h6>
                <button class="copy-btn" onclick="copyCode('comments-get-code')">
                  <i class="fas fa-copy me-1"></i>Copy
                </button>
              </div>
              <div class="code-block" id="comments-get-code">
                <code>// controllers/commentController.js
exports.getComments = async (req, res, next) => {
  try {
    const { postId } = req.params;

    // Check if post exists
    const post = await Post.findById(postId);
    if (!post) {
      return res.status(404).json({
        success: false,
        error: 'Post not found'
      });
    }

    const comments = await Comment.find({ post: postId })
      .populate('author', 'username')
      .sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      count: comments.length,
      data: comments
    });
  } catch (err) {
    next(err);
  }
};</code>
              </div>
            </div>
          </div>
        </div>

        <!-- Create Comment -->
        <div class="endpoint" onclick="toggleEndpoint('comments-create')">
          <div class="endpoint-header">
            <div class="d-flex align-items-center">
              <span class="method post">POST</span>
              <span class="endpoint-url">/api/posts/:postId/comments</span>
              <span class="status-badge status-auth">Auth Required</span>
            </div>
            <button class="endpoint-toggle" type="button">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          <div class="endpoint-description">
            Membuat komentar baru untuk post tertentu
          </div>
          <div class="endpoint-content" id="comments-create">
            <div class="parameter-list">
              <h6><i class="fas fa-key me-2"></i>Headers</h6>
              <div class="parameter-item">
                <span class="parameter-name">Authorization</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Bearer token (required)</span>
              </div>
            </div>

            <div class="parameter-list">
              <h6><i class="fas fa-route me-2"></i>Path Parameters</h6>
              <div class="parameter-item">
                <span class="parameter-name">postId</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">ID post yang akan dikomentari (required)</span>
              </div>
            </div>

            <div class="parameter-list">
              <h6><i class="fas fa-arrow-down me-2"></i>Request Body</h6>
              <div class="parameter-item">
                <span class="parameter-name">content</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Isi komentar (required)</span>
              </div>
            </div>

            <h6><i class="fas fa-arrow-up me-2"></i>Response Example</h6>
            <div class="code-block">
              <code>{
  "success": true,
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "content": "This is my comment",
    "author": {
      "id": "507f1f77bcf86cd799439012",
      "username": "johndoe"
    },
    "post": "507f1f77bcf86cd799439013",
    "createdAt": "2023-12-01T10:00:00.000Z",
    "updatedAt": "2023-12-01T10:00:00.000Z"
  }
}</code>
            </div>

            <div class="source-code-section">
              <div class="source-code-header">
                <h6><i class="fas fa-code me-2"></i>Source Code</h6>
                <button class="copy-btn" onclick="copyCode('comments-create-code')">
                  <i class="fas fa-copy me-1"></i>Copy
                </button>
              </div>
              <div class="code-block" id="comments-create-code">
                <code>// controllers/commentController.js
exports.createComment = async (req, res, next) => {
  try {
    const { postId } = req.params;
    const { content } = req.body;

    // Check if post exists
    const post = await Post.findById(postId);
    if (!post) {
      return res.status(404).json({
        success: false,
        error: 'Post not found'
      });
    }

    const comment = await Comment.create({
      content,
      author: req.user.id,
      post: postId
    });

    await comment.populate('author', 'username');

    res.status(201).json({
      success: true,
      data: comment
    });
  } catch (err) {
    next(err);
  }
};</code>
              </div>
            </div>
          </div>
        </div>

        <!-- Delete Comment -->
        <div class="endpoint" onclick="toggleEndpoint('comments-delete')">
          <div class="endpoint-header">
            <div class="d-flex align-items-center">
              <span class="method delete">DELETE</span>
              <span class="endpoint-url">/api/comments/:id</span>
              <span class="status-badge status-owner">Owner Only</span>
            </div>
            <button class="endpoint-toggle" type="button">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          <div class="endpoint-description">
            Menghapus komentar (hanya pemilik komentar yang dapat menghapus)
          </div>
          <div class="endpoint-content" id="comments-delete">
            <div class="parameter-list">
              <h6><i class="fas fa-key me-2"></i>Headers</h6>
              <div class="parameter-item">
                <span class="parameter-name">Authorization</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Bearer token (required)</span>
              </div>
            </div>

            <div class="parameter-list">
              <h6><i class="fas fa-route me-2"></i>Path Parameters</h6>
              <div class="parameter-item">
                <span class="parameter-name">id</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">ID komentar yang akan dihapus (required)</span>
              </div>
            </div>

            <h6><i class="fas fa-arrow-up me-2"></i>Response Example</h6>
            <div class="code-block">
              <code>{
  "success": true,
  "data": {}
}</code>
            </div>

            <div class="source-code-section">
              <div class="source-code-header">
                <h6><i class="fas fa-code me-2"></i>Source Code</h6>
                <button class="copy-btn" onclick="copyCode('comments-delete-code')">
                  <i class="fas fa-copy me-1"></i>Copy
                </button>
              </div>
              <div class="code-block" id="comments-delete-code">
                <code>// controllers/commentController.js
exports.deleteComment = async (req, res, next) => {
  try {
    const comment = await Comment.findById(req.params.id);

    if (!comment) {
      return res.status(404).json({
        success: false,
        error: 'Comment not found'
      });
    }

    // Check if user is the owner
    if (comment.author.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        error: 'Not authorized to delete this comment'
      });
    }

    await comment.deleteOne();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    next(err);
  }
};</code>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Account Tab -->
      <div class="tab-pane fade" id="account" role="tabpanel">
        <!-- Get Account Details -->
        <div class="endpoint" onclick="toggleEndpoint('account-get')">
          <div class="endpoint-header">
            <div class="d-flex align-items-center">
              <span class="method get">GET</span>
              <span class="endpoint-url">/api/account</span>
              <span class="status-badge status-auth">Auth Required</span>
            </div>
            <button class="endpoint-toggle" type="button">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          <div class="endpoint-description">
            Mendapatkan detail akun pengguna yang sedang login
          </div>
          <div class="endpoint-content" id="account-get">
            <div class="parameter-list">
              <h6><i class="fas fa-key me-2"></i>Headers</h6>
              <div class="parameter-item">
                <span class="parameter-name">Authorization</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Bearer token (required)</span>
              </div>
            </div>

            <h6><i class="fas fa-arrow-up me-2"></i>Response Example</h6>
            <div class="code-block">
              <code>{
  "success": true,
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "username": "johndoe",
    "email": "<EMAIL>",
    "profilePictureUrl": "/api/account/profile-picture/profile.jpg",
    "createdAt": "2023-12-01T10:00:00.000Z"
  }
}</code>
            </div>

            <div class="source-code-section">
              <div class="source-code-header">
                <h6><i class="fas fa-code me-2"></i>Source Code</h6>
                <button class="copy-btn" onclick="copyCode('account-get-code')">
                  <i class="fas fa-copy me-1"></i>Copy
                </button>
              </div>
              <div class="code-block" id="account-get-code">
                <code>// controllers/accountController.js
exports.getAccount = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);

    res.status(200).json({
      success: true,
      data: {
        id: user._id,
        username: user.username,
        email: user.email,
        profilePictureUrl: user.profilePicture ?
          `/api/account/profile-picture/${user.profilePicture}` : null,
        createdAt: user.createdAt
      }
    });
  } catch (err) {
    next(err);
  }
};</code>
              </div>
            </div>
          </div>
        </div>

        <!-- Update Account -->
        <div class="endpoint" onclick="toggleEndpoint('account-update')">
          <div class="endpoint-header">
            <div class="d-flex align-items-center">
              <span class="method put">PUT</span>
              <span class="endpoint-url">/api/account</span>
              <span class="status-badge status-auth">Auth Required</span>
            </div>
            <button class="endpoint-toggle" type="button">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          <div class="endpoint-description">
            Update informasi akun pengguna (username dan email)
          </div>
          <div class="endpoint-content" id="account-update">
            <div class="parameter-list">
              <h6><i class="fas fa-key me-2"></i>Headers</h6>
              <div class="parameter-item">
                <span class="parameter-name">Authorization</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Bearer token (required)</span>
              </div>
            </div>

            <div class="parameter-list">
              <h6><i class="fas fa-arrow-down me-2"></i>Request Body</h6>
              <div class="parameter-item">
                <span class="parameter-name">username</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Username baru (optional)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">email</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Email baru (optional)</span>
              </div>
            </div>

            <h6><i class="fas fa-arrow-up me-2"></i>Response Example</h6>
            <div class="code-block">
              <code>{
  "success": true,
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "username": "newusername",
    "email": "<EMAIL>",
    "profilePictureUrl": "/api/account/profile-picture/profile.jpg",
    "createdAt": "2023-12-01T10:00:00.000Z"
  }
}</code>
            </div>

            <div class="source-code-section">
              <div class="source-code-header">
                <h6><i class="fas fa-code me-2"></i>Source Code</h6>
                <button class="copy-btn" onclick="copyCode('account-update-code')">
                  <i class="fas fa-copy me-1"></i>Copy
                </button>
              </div>
              <div class="code-block" id="account-update-code">
                <code>// controllers/accountController.js
exports.updateAccount = async (req, res, next) => {
  try {
    const fieldsToUpdate = {
      username: req.body.username,
      email: req.body.email
    };

    // Remove undefined fields
    Object.keys(fieldsToUpdate).forEach(
      key => fieldsToUpdate[key] === undefined && delete fieldsToUpdate[key]
    );

    const user = await User.findByIdAndUpdate(
      req.user.id,
      fieldsToUpdate,
      {
        new: true,
        runValidators: true
      }
    );

    res.status(200).json({
      success: true,
      data: {
        id: user._id,
        username: user.username,
        email: user.email,
        profilePictureUrl: user.profilePicture ?
          `/api/account/profile-picture/${user.profilePicture}` : null,
        createdAt: user.createdAt
      }
    });
  } catch (err) {
    next(err);
  }
};</code>
              </div>
            </div>
          </div>
        </div>

        <!-- Delete Account -->
        <div class="endpoint" onclick="toggleEndpoint('account-delete')">
          <div class="endpoint-header">
            <div class="d-flex align-items-center">
              <span class="method delete">DELETE</span>
              <span class="endpoint-url">/api/account</span>
              <span class="status-badge status-auth">Auth Required</span>
            </div>
            <button class="endpoint-toggle" type="button">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          <div class="endpoint-description">
            Menghapus akun pengguna beserta semua data terkait
          </div>
          <div class="endpoint-content" id="account-delete">
            <div class="parameter-list">
              <h6><i class="fas fa-key me-2"></i>Headers</h6>
              <div class="parameter-item">
                <span class="parameter-name">Authorization</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Bearer token (required)</span>
              </div>
            </div>

            <h6><i class="fas fa-arrow-up me-2"></i>Response Example</h6>
            <div class="code-block">
              <code>{
  "success": true,
  "data": {}
}</code>
            </div>

            <div class="source-code-section">
              <div class="source-code-header">
                <h6><i class="fas fa-code me-2"></i>Source Code</h6>
                <button class="copy-btn" onclick="copyCode('account-delete-code')">
                  <i class="fas fa-copy me-1"></i>Copy
                </button>
              </div>
              <div class="code-block" id="account-delete-code">
                <code>// controllers/accountController.js
exports.deleteAccount = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);

    // Delete profile picture if exists
    if (user.profilePicture) {
      try {
        await deleteImage(user.profilePicture);
      } catch (err) {
        console.log('Error deleting profile picture:', err.message);
      }
    }

    // Delete all user's posts
    await Post.deleteMany({ userId: req.user.id });

    // Delete all user's comments
    await Comment.deleteMany({ userId: req.user.id });

    // Delete user
    await User.findByIdAndDelete(req.user.id);

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    next(err);
  }
};</code>
              </div>
            </div>
          </div>
        </div>

        <!-- Upload Profile Picture -->
        <div class="endpoint" onclick="toggleEndpoint('account-upload-picture')">
          <div class="endpoint-header">
            <div class="d-flex align-items-center">
              <span class="method post">POST</span>
              <span class="endpoint-url">/api/account/profile-picture</span>
              <span class="status-badge status-auth">Auth Required</span>
            </div>
            <button class="endpoint-toggle" type="button">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          <div class="endpoint-description">
            Upload atau update foto profil pengguna
          </div>
          <div class="endpoint-content" id="account-upload-picture">
            <div class="parameter-list">
              <h6><i class="fas fa-key me-2"></i>Headers</h6>
              <div class="parameter-item">
                <span class="parameter-name">Authorization</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Bearer token (required)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">Content-Type</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">multipart/form-data (for file upload)</span>
              </div>
            </div>

            <div class="parameter-list">
              <h6><i class="fas fa-arrow-down me-2"></i>Form Data</h6>
              <div class="parameter-item">
                <span class="parameter-name">profilePicture</span>
                <span class="parameter-type">file</span>
                <span class="parameter-description">File gambar profil (required, max 5MB)</span>
              </div>
            </div>

            <h6><i class="fas fa-arrow-up me-2"></i>Response Example</h6>
            <div class="code-block">
              <code>{
  "success": true,
  "data": {
    "profilePictureUrl": "/api/account/profile-picture/*************-profile.jpg",
    "message": "Profile picture uploaded successfully"
  }
}</code>
            </div>

            <div class="source-code-section">
              <div class="source-code-header">
                <h6><i class="fas fa-code me-2"></i>Source Code</h6>
                <button class="copy-btn" onclick="copyCode('account-upload-picture-code')">
                  <i class="fas fa-copy me-1"></i>Copy
                </button>
              </div>
              <div class="code-block" id="account-upload-picture-code">
                <code>// controllers/accountController.js
exports.uploadProfilePicture = async (req, res, next) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        error: 'Please upload a file'
      });
    }

    const user = await User.findById(req.user.id);

    // Delete old profile picture if exists
    if (user.profilePicture) {
      try {
        await deleteImage(user.profilePicture);
      } catch (err) {
        console.log('Error deleting old profile picture:', err.message);
      }
    }

    // Update user with new profile picture filename
    user.profilePicture = req.file.filename;
    await user.save();

    res.status(200).json({
      success: true,
      data: {
        id: user._id,
        username: user.username,
        email: user.email,
        profilePictureUrl: `/api/account/profile-picture/${user.profilePicture}`,
        createdAt: user.createdAt
      }
    });
  } catch (err) {
    next(err);
  }
};</code>
              </div>
            </div>
          </div>
        </div>

        <!-- Get Profile Picture -->
        <div class="endpoint" onclick="toggleEndpoint('account-get-picture')">
          <div class="endpoint-header">
            <div class="d-flex align-items-center">
              <span class="method get">GET</span>
              <span class="endpoint-url">/api/account/profile-picture/:filename</span>
              <span class="status-badge status-public">Public</span>
            </div>
            <button class="endpoint-toggle" type="button">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          <div class="endpoint-description">
            Mendapatkan file gambar profil pengguna
          </div>
          <div class="endpoint-content" id="account-get-picture">
            <div class="parameter-list">
              <h6><i class="fas fa-route me-2"></i>Path Parameters</h6>
              <div class="parameter-item">
                <span class="parameter-name">filename</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Nama file gambar profil (required)</span>
              </div>
            </div>

            <h6><i class="fas fa-arrow-up me-2"></i>Response</h6>
            <div class="code-block">
              <code>File gambar dengan Content-Type yang sesuai (image/jpeg, image/png, dll)</code>
            </div>

            <div class="source-code-section">
              <div class="source-code-header">
                <h6><i class="fas fa-code me-2"></i>Source Code</h6>
                <button class="copy-btn" onclick="copyCode('account-get-picture-code')">
                  <i class="fas fa-copy me-1"></i>Copy
                </button>
              </div>
              <div class="code-block" id="account-get-picture-code">
                <code>// controllers/accountController.js
exports.getProfilePicture = async (req, res, next) => {
  try {
    await streamImage(req.params.filename, res);
  } catch (err) {
    next(err);
  }
};</code>
              </div>
            </div>
          </div>
        </div>

        <!-- Delete Profile Picture -->
        <div class="endpoint" onclick="toggleEndpoint('account-delete-picture')">
          <div class="endpoint-header">
            <div class="d-flex align-items-center">
              <span class="method delete">DELETE</span>
              <span class="endpoint-url">/api/account/profile-picture</span>
              <span class="status-badge status-auth">Auth Required</span>
            </div>
            <button class="endpoint-toggle" type="button">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          <div class="endpoint-description">
            Menghapus foto profil pengguna
          </div>
          <div class="endpoint-content" id="account-delete-picture">
            <div class="parameter-list">
              <h6><i class="fas fa-key me-2"></i>Headers</h6>
              <div class="parameter-item">
                <span class="parameter-name">Authorization</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Bearer token (required)</span>
              </div>
            </div>

            <h6><i class="fas fa-arrow-up me-2"></i>Response Example</h6>
            <div class="code-block">
              <code>{
  "success": true,
  "data": {
    "message": "Profile picture deleted successfully"
  }
}</code>
            </div>

            <div class="source-code-section">
              <div class="source-code-header">
                <h6><i class="fas fa-code me-2"></i>Source Code</h6>
                <button class="copy-btn" onclick="copyCode('account-delete-picture-code')">
                  <i class="fas fa-copy me-1"></i>Copy
                </button>
              </div>
              <div class="code-block" id="account-delete-picture-code">
                <code>// controllers/accountController.js
exports.deleteProfilePicture = async (req, res, next) => {
  try {
    const user = await User.findById(req.user.id);

    if (!user.profilePicture) {
      return res.status(400).json({
        success: false,
        error: 'No profile picture to delete'
      });
    }

    // Delete the image file
    try {
      await deleteImage(user.profilePicture);
    } catch (err) {
      console.log('Error deleting profile picture file:', err.message);
    }

    // Remove profile picture from user
    user.profilePicture = null;
    await user.save();

    res.status(200).json({
      success: true,
      data: {
        id: user._id,
        username: user.username,
        email: user.email,
        profilePictureUrl: null,
        createdAt: user.createdAt
      }
    });
  } catch (err) {
    next(err);
  }
};</code>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Categories Tab -->
      <div class="tab-pane fade" id="categories" role="tabpanel">
        <!-- Get All Categories -->
        <div class="endpoint" onclick="toggleEndpoint('categories-getall')">
          <div class="endpoint-header">
            <div class="d-flex align-items-center">
              <span class="method get">GET</span>
              <span class="endpoint-url">/api/categories</span>
              <span class="status-badge status-public">Public</span>
            </div>
            <button class="endpoint-toggle" type="button">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          <div class="endpoint-description">
            Mendapatkan semua kategori pembelajaran
          </div>
          <div class="endpoint-content" id="categories-getall">
            <div class="parameter-list">
              <h6><i class="fas fa-search me-2"></i>Query Parameters (Optional)</h6>
              <div class="parameter-item">
                <span class="parameter-name">active</span>
                <span class="parameter-type">boolean</span>
                <span class="parameter-description">Filter kategori aktif (default: true)</span>
              </div>
            </div>

            <h6><i class="fas fa-arrow-up me-2"></i>Response Example</h6>
            <div class="code-block">
              <code>{
  "success": true,
  "count": 5,
  "data": [
    {
      "id": "507f1f77bcf86cd799439011",
      "name": "Web Development",
      "description": "Learn modern web development technologies",
      "color": "#3B82F6",
      "icon": "fas fa-code",
      "isActive": true,
      "createdAt": "2023-12-01T10:00:00.000Z"
    }
  ]
}</code>
            </div>

            <div class="source-code-section">
              <div class="source-code-header">
                <h6><i class="fas fa-code me-2"></i>Source Code</h6>
                <button class="copy-btn" onclick="copyCode('categories-getall-code')">
                  <i class="fas fa-copy me-1"></i>Copy
                </button>
              </div>
              <div class="code-block" id="categories-getall-code">
                <code>// controllers/categoryController.js
exports.getCategories = async (req, res, next) => {
  try {
    const { active = true } = req.query;

    const filter = {};
    if (active !== undefined) {
      filter.isActive = active === 'true';
    }

    const categories = await Category.find(filter)
      .sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      count: categories.length,
      data: categories
    });
  } catch (err) {
    next(err);
  }
};</code>
              </div>
            </div>
          </div>
        </div>

        <!-- Create Category -->
        <div class="endpoint" onclick="toggleEndpoint('categories-create')">
          <div class="endpoint-header">
            <div class="d-flex align-items-center">
              <span class="method post">POST</span>
              <span class="endpoint-url">/api/categories</span>
              <span class="status-badge status-auth">Auth Required</span>
            </div>
            <button class="endpoint-toggle" type="button">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          <div class="endpoint-description">
            Membuat kategori pembelajaran baru
          </div>
          <div class="endpoint-content" id="categories-create">
            <div class="parameter-list">
              <h6><i class="fas fa-key me-2"></i>Headers</h6>
              <div class="parameter-item">
                <span class="parameter-name">Authorization</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Bearer token (required)</span>
              </div>
            </div>

            <div class="parameter-list">
              <h6><i class="fas fa-arrow-down me-2"></i>Request Body</h6>
              <div class="parameter-item">
                <span class="parameter-name">name</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Nama kategori (required)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">description</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Deskripsi kategori (optional)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">color</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Warna hex untuk kategori (optional, default: #3B82F6)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">icon</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Font Awesome icon class (optional)</span>
              </div>
            </div>

            <h6><i class="fas fa-arrow-up me-2"></i>Response Example</h6>
            <div class="code-block">
              <code>{
  "success": true,
  "data": {
    "id": "507f1f77bcf86cd799439011",
    "name": "Web Development",
    "description": "Learn modern web development technologies",
    "color": "#3B82F6",
    "icon": "fas fa-code",
    "isActive": true,
    "createdAt": "2023-12-01T10:00:00.000Z"
  }
}</code>
            </div>

            <div class="source-code-section">
              <div class="source-code-header">
                <h6><i class="fas fa-code me-2"></i>Source Code</h6>
                <button class="copy-btn" onclick="copyCode('categories-create-code')">
                  <i class="fas fa-copy me-1"></i>Copy
                </button>
              </div>
              <div class="code-block" id="categories-create-code">
                <code>// controllers/categoryController.js
exports.createCategory = async (req, res, next) => {
  try {
    const { name, description, color = '#3B82F6', icon } = req.body;

    // Check if category name already exists
    const existingCategory = await Category.findOne({
      name: { $regex: new RegExp(`^${name}$`, 'i') }
    });

    if (existingCategory) {
      return res.status(400).json({
        success: false,
        error: 'Category with this name already exists'
      });
    }

    const category = await Category.create({
      name,
      description,
      color,
      icon
    });

    res.status(201).json({
      success: true,
      data: category
    });
  } catch (err) {
    next(err);
  }
};</code>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Learning Tab -->
      <div class="tab-pane fade" id="learning" role="tabpanel">
        <!-- Get All Learning Content -->
        <div class="endpoint" onclick="toggleEndpoint('learning-getall')">
          <div class="endpoint-header">
            <div class="d-flex align-items-center">
              <span class="method get">GET</span>
              <span class="endpoint-url">/api/learning</span>
              <span class="status-badge status-public">Public</span>
            </div>
            <button class="endpoint-toggle" type="button">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          <div class="endpoint-description">
            Mendapatkan semua konten pembelajaran dengan filtering dan pagination
          </div>
          <div class="endpoint-content" id="learning-getall">
            <div class="parameter-list">
              <h6><i class="fas fa-search me-2"></i>Query Parameters (Optional)</h6>
              <div class="parameter-item">
                <span class="parameter-name">page</span>
                <span class="parameter-type">number</span>
                <span class="parameter-description">Nomor halaman (default: 1)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">limit</span>
                <span class="parameter-type">number</span>
                <span class="parameter-description">Jumlah item per halaman (default: 10)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">category</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Filter berdasarkan kategori ID</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">difficulty</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Filter berdasarkan tingkat kesulitan (beginner/intermediate/advanced)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">search</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Pencarian berdasarkan kata kunci</span>
              </div>
            </div>

            <h6><i class="fas fa-arrow-up me-2"></i>Response Example</h6>
            <div class="code-block">
              <code>{
  "success": true,
  "count": 10,
  "pagination": {
    "total": 50,
    "page": 1,
    "limit": 10,
    "pages": 5
  },
  "data": [
    {
      "id": "507f1f77bcf86cd799439011",
      "title": "Introduction to Node.js",
      "content": "Learn the basics of Node.js...",
      "summary": "A comprehensive guide to Node.js",
      "difficulty": "beginner",
      "estimatedTime": 15,
      "imageUrl": "/api/learning/image/learning-image.jpg",
      "categories": [
        {
          "id": "507f1f77bcf86cd799439012",
          "name": "Web Development",
          "color": "#3B82F6"
        }
      ],
      "userId": {
        "id": "507f1f77bcf86cd799439013",
        "username": "instructor"
      },
      "isPublished": true,
      "viewCount": 100,
      "createdAt": "2023-12-01T10:00:00.000Z"
    }
  ]
}</code>
            </div>

            <div class="source-code-section">
              <div class="source-code-header">
                <h6><i class="fas fa-code me-2"></i>Source Code</h6>
                <button class="copy-btn" onclick="copyCode('learning-getall-code')">
                  <i class="fas fa-copy me-1"></i>Copy
                </button>
              </div>
              <div class="code-block" id="learning-getall-code">
                <code>// controllers/learningController.js
exports.getLearningContent = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    // Build filter object
    const filter = { isPublished: true };

    if (req.query.category) {
      filter.categories = { $in: [req.query.category] };
    }

    if (req.query.difficulty) {
      filter.difficulty = req.query.difficulty;
    }

    if (req.query.search) {
      filter.$or = [
        { title: { $regex: req.query.search, $options: 'i' } },
        { content: { $regex: req.query.search, $options: 'i' } }
      ];
    }

    const total = await Learning.countDocuments(filter);
    const learningContent = await Learning.find(filter)
      .populate('categories', 'name color')
      .populate('userId', 'username')
      .sort({ createdAt: -1 })
      .limit(limit)
      .skip(startIndex);

    const pagination = {
      total,
      page,
      limit,
      pages: Math.ceil(total / limit)
    };

    res.status(200).json({
      success: true,
      count: learningContent.length,
      pagination,
      data: learningContent
    });
  } catch (err) {
    next(err);
  }
};</code>
              </div>
            </div>
          </div>
        </div>

        <!-- Create Learning Content -->
        <div class="endpoint" onclick="toggleEndpoint('learning-create')">
          <div class="endpoint-header">
            <div class="d-flex align-items-center">
              <span class="method post">POST</span>
              <span class="endpoint-url">/api/learning</span>
              <span class="status-badge status-auth">Auth Required</span>
            </div>
            <button class="endpoint-toggle" type="button">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          <div class="endpoint-description">
            Membuat konten pembelajaran baru dengan dukungan upload gambar
          </div>
          <div class="endpoint-content" id="learning-create">
            <div class="parameter-list">
              <h6><i class="fas fa-key me-2"></i>Headers</h6>
              <div class="parameter-item">
                <span class="parameter-name">Authorization</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Bearer token (required)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">Content-Type</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">multipart/form-data (for file upload)</span>
              </div>
            </div>

            <div class="parameter-list">
              <h6><i class="fas fa-arrow-down me-2"></i>Form Data</h6>
              <div class="parameter-item">
                <span class="parameter-name">title</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Judul konten pembelajaran (required)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">content</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Konten pembelajaran lengkap (required)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">summary</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Ringkasan konten (optional)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">difficulty</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Tingkat kesulitan: beginner, intermediate, advanced (required)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">estimatedTime</span>
                <span class="parameter-type">number</span>
                <span class="parameter-description">Estimasi waktu baca dalam menit (required)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">categories</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Array ID kategori dalam format JSON string (required)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">image</span>
                <span class="parameter-type">file</span>
                <span class="parameter-description">File gambar konten (optional, max 5MB)</span>
              </div>
            </div>

            <div class="source-code-section">
              <div class="source-code-header">
                <h6><i class="fas fa-code me-2"></i>Source Code</h6>
                <button class="copy-btn" onclick="copyCode('learning-create-code')">
                  <i class="fas fa-copy me-1"></i>Copy
                </button>
              </div>
              <div class="code-block" id="learning-create-code">
                <code>// controllers/learningController.js
exports.createLearningContent = async (req, res, next) => {
  try {
    const { title, content, summary, difficulty, estimatedTime, categories } = req.body;

    // Parse categories from JSON string
    let parsedCategories;
    try {
      parsedCategories = JSON.parse(categories);
    } catch (error) {
      return res.status(400).json({
        success: false,
        error: 'Invalid categories format. Must be a JSON array.'
      });
    }

    const learningData = {
      title,
      content,
      summary,
      difficulty,
      estimatedTime: parseInt(estimatedTime),
      categories: parsedCategories,
      userId: req.user.id
    };

    // Handle image upload if present
    if (req.file) {
      learningData.image = req.file.filename;
    }

    const learning = await Learning.create(learningData);
    await learning.populate('categories', 'name color');
    await learning.populate('userId', 'username');

    res.status(201).json({
      success: true,
      data: learning
    });
  } catch (err) {
    next(err);
  }
};</code>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Progress Tab -->
      <div class="tab-pane fade" id="progress" role="tabpanel">
        <!-- Bookmark Learning Content -->
        <div class="endpoint" onclick="toggleEndpoint('progress-bookmark')">
          <div class="endpoint-header">
            <div class="d-flex align-items-center">
              <span class="method post">POST</span>
              <span class="endpoint-url">/api/learning/:id/bookmark</span>
              <span class="status-badge status-auth">Auth Required</span>
            </div>
            <button class="endpoint-toggle" type="button">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          <div class="endpoint-description">
            Menandai konten pembelajaran sebagai bookmark
          </div>
          <div class="endpoint-content" id="progress-bookmark">
            <div class="parameter-list">
              <h6><i class="fas fa-key me-2"></i>Headers</h6>
              <div class="parameter-item">
                <span class="parameter-name">Authorization</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Bearer token (required)</span>
              </div>
            </div>

            <div class="parameter-list">
              <h6><i class="fas fa-route me-2"></i>Path Parameters</h6>
              <div class="parameter-item">
                <span class="parameter-name">id</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">ID konten pembelajaran (required)</span>
              </div>
            </div>

            <h6><i class="fas fa-arrow-up me-2"></i>Response Example</h6>
            <div class="code-block">
              <code>{
  "success": true,
  "data": {
    "userId": "507f1f77bcf86cd799439011",
    "learningId": "507f1f77bcf86cd799439012",
    "isBookmarked": true,
    "bookmarkedAt": "2023-12-01T10:00:00.000Z"
  }
}</code>
            </div>

            <div class="source-code-section">
              <div class="source-code-header">
                <h6><i class="fas fa-code me-2"></i>Source Code</h6>
                <button class="copy-btn" onclick="copyCode('progress-bookmark-code')">
                  <i class="fas fa-copy me-1"></i>Copy
                </button>
              </div>
              <div class="code-block" id="progress-bookmark-code">
                <code>// controllers/userProgressController.js
exports.bookmarkLearning = async (req, res, next) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // Check if learning content exists
    const learning = await Learning.findById(id);
    if (!learning) {
      return res.status(404).json({
        success: false,
        error: 'Learning content not found'
      });
    }

    // Check if already bookmarked
    let progress = await UserProgress.findOne({
      userId,
      learningId: id
    });

    if (progress) {
      if (progress.isBookmarked) {
        return res.status(400).json({
          success: false,
          error: 'Content already bookmarked'
        });
      }
      progress.isBookmarked = true;
      progress.bookmarkedAt = new Date();
    } else {
      progress = await UserProgress.create({
        userId,
        learningId: id,
        isBookmarked: true,
        bookmarkedAt: new Date()
      });
    }

    await progress.save();

    res.status(200).json({
      success: true,
      data: progress
    });
  } catch (err) {
    next(err);
  }
};</code>
              </div>
            </div>
          </div>
        </div>

        <!-- Get User Progress -->
        <div class="endpoint" onclick="toggleEndpoint('progress-get')">
          <div class="endpoint-header">
            <div class="d-flex align-items-center">
              <span class="method get">GET</span>
              <span class="endpoint-url">/api/learning/progress</span>
              <span class="status-badge status-auth">Auth Required</span>
            </div>
            <button class="endpoint-toggle" type="button">
              <i class="fas fa-chevron-down"></i>
            </button>
          </div>
          <div class="endpoint-description">
            Mendapatkan statistik dan progress pembelajaran pengguna
          </div>
          <div class="endpoint-content" id="progress-get">
            <div class="parameter-list">
              <h6><i class="fas fa-key me-2"></i>Headers</h6>
              <div class="parameter-item">
                <span class="parameter-name">Authorization</span>
                <span class="parameter-type">string</span>
                <span class="parameter-description">Bearer token (required)</span>
              </div>
            </div>

            <div class="parameter-list">
              <h6><i class="fas fa-search me-2"></i>Query Parameters (Optional)</h6>
              <div class="parameter-item">
                <span class="parameter-name">page</span>
                <span class="parameter-type">number</span>
                <span class="parameter-description">Nomor halaman (default: 1)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">limit</span>
                <span class="parameter-type">number</span>
                <span class="parameter-description">Jumlah item per halaman (default: 10)</span>
              </div>
              <div class="parameter-item">
                <span class="parameter-name">completed</span>
                <span class="parameter-type">boolean</span>
                <span class="parameter-description">Filter berdasarkan status selesai</span>
              </div>
            </div>

            <h6><i class="fas fa-arrow-up me-2"></i>Response Example</h6>
            <div class="code-block">
              <code>{
  "success": true,
  "count": 5,
  "pagination": {
    "total": 15,
    "page": 1,
    "limit": 10,
    "pages": 2
  },
  "stats": {
    "totalStarted": 10,
    "totalCompleted": 5,
    "totalBookmarked": 3,
    "averageProgress": 75.5
  },
  "data": [
    {
      "userId": "507f1f77bcf86cd799439011",
      "learningId": {
        "id": "507f1f77bcf86cd799439012",
        "title": "Introduction to Node.js",
        "difficulty": "beginner"
      },
      "isBookmarked": true,
      "isCompleted": false,
      "readingProgress": 75,
      "completedAt": null,
      "bookmarkedAt": "2023-12-01T10:00:00.000Z"
    }
  ]
}</code>
            </div>

            <div class="source-code-section">
              <div class="source-code-header">
                <h6><i class="fas fa-code me-2"></i>Source Code</h6>
                <button class="copy-btn" onclick="copyCode('progress-get-code')">
                  <i class="fas fa-copy me-1"></i>Copy
                </button>
              </div>
              <div class="code-block" id="progress-get-code">
                <code>// controllers/userProgressController.js
exports.getUserProgress = async (req, res, next) => {
  try {
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const startIndex = (page - 1) * limit;

    const filter = { userId: req.user.id };

    if (req.query.completed !== undefined) {
      filter.isCompleted = req.query.completed === 'true';
    }

    // Get statistics
    const stats = await UserProgress.aggregate([
      { $match: { userId: new mongoose.Types.ObjectId(req.user.id) } },
      {
        $group: {
          _id: null,
          totalStarted: { $sum: 1 },
          totalCompleted: { $sum: { $cond: ['$isCompleted', 1, 0] } },
          totalBookmarked: { $sum: { $cond: ['$isBookmarked', 1, 0] } },
          averageProgress: { $avg: '$readingProgress' }
        }
      }
    ]);

    const total = await UserProgress.countDocuments(filter);
    const progress = await UserProgress.find(filter)
      .populate('learningId', 'title difficulty estimatedTime')
      .sort({ updatedAt: -1 })
      .limit(limit)
      .skip(startIndex);

    const pagination = {
      total,
      page,
      limit,
      pages: Math.ceil(total / limit)
    };

    res.status(200).json({
      success: true,
      count: progress.length,
      pagination,
      stats: stats[0] || {
        totalStarted: 0,
        totalCompleted: 0,
        totalBookmarked: 0,
        averageProgress: 0
      },
      data: progress
    });
  } catch (err) {
    next(err);
  }
};</code>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Errors Tab -->
      <div class="tab-pane fade" id="errors" role="tabpanel">
        <div class="card">
          <div class="card-header">
            <h2 class="h5 mb-0"><i class="fas fa-exclamation-triangle me-2"></i>Error Handling</h2>
          </div>
          <div class="card-body">
            <p class="lead">API ini menggunakan format error yang konsisten untuk semua respons error:</p>

            <h4><i class="fas fa-code me-2"></i>Error Response Format</h4>
            <div class="code-block">
              <code>{
  "success": false,
  "error": "Pesan error yang deskriptif",
  "requestId": "unique-request-id"
}</code>
            </div>

            <h4 class="mt-4"><i class="fas fa-list me-2"></i>HTTP Status Codes</h4>
            <div class="table-responsive">
              <table class="table table-striped table-hover">
                <thead class="table-dark">
                  <tr>
                    <th><i class="fas fa-hashtag me-1"></i>Kode</th>
                    <th><i class="fas fa-info-circle me-1"></i>Deskripsi</th>
                    <th><i class="fas fa-lightbulb me-1"></i>Contoh Kasus</th>
                  </tr>
                </thead>
                <tbody>
                  <tr class="table-success">
                    <td><span class="badge bg-success">200 OK</span></td>
                    <td>Permintaan berhasil</td>
                    <td>GET, PUT, DELETE berhasil</td>
                  </tr>
                  <tr class="table-success">
                    <td><span class="badge bg-success">201 Created</span></td>
                    <td>Resource berhasil dibuat</td>
                    <td>POST berhasil membuat data baru</td>
                  </tr>
                  <tr class="table-warning">
                    <td><span class="badge bg-warning text-dark">400 Bad Request</span></td>
                    <td>Permintaan tidak valid</td>
                    <td>Validasi gagal, format tidak valid</td>
                  </tr>
                  <tr class="table-warning">
                    <td><span class="badge bg-warning text-dark">401 Unauthorized</span></td>
                    <td>Autentikasi diperlukan atau gagal</td>
                    <td>Token tidak valid atau tidak ada</td>
                  </tr>
                  <tr class="table-warning">
                    <td><span class="badge bg-warning text-dark">403 Forbidden</span></td>
                    <td>Tidak memiliki izin untuk mengakses resource</td>
                    <td>Mencoba mengedit post orang lain</td>
                  </tr>
                  <tr class="table-danger">
                    <td><span class="badge bg-danger">404 Not Found</span></td>
                    <td>Resource tidak ditemukan</td>
                    <td>ID post/user tidak ada</td>
                  </tr>
                  <tr class="table-warning">
                    <td><span class="badge bg-warning text-dark">429 Too Many Requests</span></td>
                    <td>Rate limit terlampaui</td>
                    <td>Terlalu banyak request dalam waktu singkat</td>
                  </tr>
                  <tr class="table-danger">
                    <td><span class="badge bg-danger">500 Internal Server Error</span></td>
                    <td>Kesalahan server</td>
                    <td>Database error, server crash</td>
                  </tr>
                </tbody>
              </table>
            </div>

            <h4 class="mt-4"><i class="fas fa-shield-alt me-2"></i>Common Error Examples</h4>
            <div class="row">
              <div class="col-md-6">
                <h6>Validation Error (400)</h6>
                <div class="code-block">
                  <code>{
  "success": false,
  "error": "Validation failed: title is required"
}</code>
                </div>
              </div>
              <div class="col-md-6">
                <h6>Authentication Error (401)</h6>
                <div class="code-block">
                  <code>{
  "success": false,
  "error": "Invalid or expired token"
}</code>
                </div>
              </div>
            </div>

            <div class="row mt-3">
              <div class="col-md-6">
                <h6>Authorization Error (403)</h6>
                <div class="code-block">
                  <code>{
  "success": false,
  "error": "Not authorized to delete this post"
}</code>
                </div>
              </div>
              <div class="col-md-6">
                <h6>Not Found Error (404)</h6>
                <div class="code-block">
                  <code>{
  "success": false,
  "error": "Post not found"
}</code>
                </div>
              </div>
            </div>

            <div class="alert alert-info mt-4">
              <h6><i class="fas fa-info-circle me-2"></i>Tips untuk Error Handling</h6>
              <ul class="mb-0">
                <li>Selalu periksa field <code>success</code> untuk menentukan status response</li>
                <li>Gunakan HTTP status code untuk handling error di level aplikasi</li>
                <li>Field <code>error</code> berisi pesan yang dapat ditampilkan ke user</li>
                <li>Implementasikan retry logic untuk error 5xx</li>
                <li>Log semua error untuk debugging dan monitoring</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <footer class="bg-dark text-white mt-5 py-3">
    <div class="container text-center">
      <p class="mb-0">API Project &copy; 2023</p>
    </div>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>

  <script>
    // Toggle endpoint content
    function toggleEndpoint(endpointId) {
      const content = document.getElementById(endpointId);
      const toggle = content.parentElement.querySelector('.endpoint-toggle i');

      if (content.classList.contains('show')) {
        content.classList.remove('show');
        toggle.classList.remove('fa-chevron-up');
        toggle.classList.add('fa-chevron-down');
      } else {
        // Find the current active tab
        const activeTab = document.querySelector('.tab-pane.active');

        // Close all other endpoints only within the current active tab
        if (activeTab) {
          activeTab.querySelectorAll('.endpoint-content.show').forEach(el => {
            el.classList.remove('show');
            const otherToggle = el.parentElement.querySelector('.endpoint-toggle i');
            if (otherToggle) {
              otherToggle.classList.remove('fa-chevron-up');
              otherToggle.classList.add('fa-chevron-down');
            }
          });
        }

        // Open this endpoint
        content.classList.add('show');
        toggle.classList.remove('fa-chevron-down');
        toggle.classList.add('fa-chevron-up');
      }
    }

    // Copy code to clipboard
    async function copyCode(codeId) {
      const codeElement = document.getElementById(codeId);
      const code = codeElement.textContent;

      try {
        await navigator.clipboard.writeText(code);

        // Show feedback
        const button = event.target.closest('.copy-btn');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check me-1"></i>Copied!';
        button.style.background = '#28a745';

        setTimeout(() => {
          button.innerHTML = originalText;
          button.style.background = '#4a5568';
        }, 2000);
      } catch (err) {
        console.error('Failed to copy code: ', err);

        // Fallback for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = code;
        document.body.appendChild(textArea);
        textArea.select();
        document.execCommand('copy');
        document.body.removeChild(textArea);

        // Show feedback
        const button = event.target.closest('.copy-btn');
        const originalText = button.innerHTML;
        button.innerHTML = '<i class="fas fa-check me-1"></i>Copied!';
        button.style.background = '#28a745';

        setTimeout(() => {
          button.innerHTML = originalText;
          button.style.background = '#4a5568';
        }, 2000);
      }
    }

    // Prevent event bubbling on copy button clicks
    document.addEventListener('click', function(e) {
      if (e.target.closest('.copy-btn')) {
        e.stopPropagation();
      }
    });

    // Add smooth scrolling to tab navigation and reset endpoint states
    document.querySelectorAll('.nav-link').forEach(link => {
      link.addEventListener('click', function() {
        // Close all open endpoints when switching tabs
        document.querySelectorAll('.endpoint-content.show').forEach(el => {
          el.classList.remove('show');
          const toggle = el.parentElement.querySelector('.endpoint-toggle i');
          if (toggle) {
            toggle.classList.remove('fa-chevron-up');
            toggle.classList.add('fa-chevron-down');
          }
        });

        setTimeout(() => {
          window.scrollTo({
            top: 0,
            behavior: 'smooth'
          });
        }, 100);
      });
    });

    // Add search functionality
    function searchEndpoints() {
      const searchTerm = document.getElementById('searchInput').value.toLowerCase();
      const endpoints = document.querySelectorAll('.endpoint');

      endpoints.forEach(endpoint => {
        const text = endpoint.textContent.toLowerCase();
        if (text.includes(searchTerm)) {
          endpoint.style.display = 'block';
        } else {
          endpoint.style.display = 'none';
        }
      });
    }

    // Initialize syntax highlighting
    document.addEventListener('DOMContentLoaded', function() {
      if (typeof Prism !== 'undefined') {
        Prism.highlightAll();
      }
    });
  </script>
</body>
</html>
