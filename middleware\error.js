/**
 * Global error handler middleware
 * Handles different types of errors and formats responses consistently
 */
const logger = require('../utils/logger');
const config = require('../config/config');

const errorHandler = (err, req, res, next) => {
  let error = { ...err };
  error.message = err.message;

  // Generate request ID if not exists
  const requestId = req.id || `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

  // Log error details with appropriate level
  const logMeta = {
    requestId,
    path: req.path,
    method: req.method,
    name: err.name,
    code: err.code,
    stack: config.server.isDevelopment ? err.stack : undefined
  };

  // Mongoose bad ObjectId
  if (err.name === 'CastError') {
    const message = `Resource not found with id of ${err.value}`;
    error = { message, statusCode: 404 };
    logger.warn(`Cast Error: ${message}`, logMeta);
  }

  // Mongoose duplicate key
  else if (err.code === 11000) {
    const field = Object.keys(err.keyValue)[0];
    const message = `Duplicate field value entered for ${field}. Please use another value.`;
    error = { message, statusCode: 400 };
    logger.warn(`Duplicate Key: ${message}`, logMeta);
  }

  // Mongoose validation error
  else if (err.name === 'ValidationError') {
    const message = Object.values(err.errors).map(val => val.message);
    error = { message, statusCode: 400 };
    logger.warn(`Validation Error: ${message}`, logMeta);
  }

  // JWT errors
  else if (err.name === 'JsonWebTokenError') {
    error = { message: 'Invalid token. Please log in again.', statusCode: 401 };
    logger.warn('JWT Error: Invalid token', logMeta);
  }

  else if (err.name === 'TokenExpiredError') {
    error = { message: 'Token expired. Please log in again.', statusCode: 401 };
    logger.warn('JWT Error: Token expired', logMeta);
  }

  // SyntaxError for JSON parsing
  else if (err instanceof SyntaxError && err.status === 400 && 'body' in err) {
    error = { message: 'Invalid JSON payload', statusCode: 400 };
    logger.warn('Syntax Error: Invalid JSON payload', logMeta);
  }

  // Default server error
  else {
    logger.error(`Unhandled Error: ${err.message}`, {
      ...logMeta,
      stack: err.stack // Always log stack for unhandled errors
    });
  }

  // Default response
  const statusCode = error.statusCode || 500;
  const errorResponse = {
    success: false,
    error: error.message || 'Server Error',
    requestId,
    ...(config.server.isDevelopment && { stack: err.stack })
  };

  res.status(statusCode).json(errorResponse);
};

module.exports = errorHandler;
