const UserProgress = require('../models/UserProgress');
const Learning = require('../models/Learning');

// @desc    Bookmark learning content
// @route   POST /api/learning/:id/bookmark
// @access  Private
exports.bookmarkLearning = async (req, res, next) => {
  try {
    const learning = await Learning.findById(req.params.id);

    if (!learning) {
      return res.status(404).json({
        success: false,
        error: 'Learning content not found'
      });
    }

    let progress = await UserProgress.findOne({
      userId: req.user.id,
      learningId: req.params.id
    });

    if (progress) {
      progress.isBookmarked = true;
      await progress.save();
    } else {
      progress = await UserProgress.create({
        userId: req.user.id,
        learningId: req.params.id,
        isBookmarked: true
      });
    }

    res.status(200).json({
      success: true,
      data: progress
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Remove bookmark from learning content
// @route   DELETE /api/learning/:id/bookmark
// @access  Private
exports.removeBookmark = async (req, res, next) => {
  try {
    const progress = await UserProgress.findOne({
      userId: req.user.id,
      learningId: req.params.id
    });

    if (!progress) {
      return res.status(404).json({
        success: false,
        error: 'Bookmark not found'
      });
    }

    progress.isBookmarked = false;
    await progress.save();

    res.status(200).json({
      success: true,
      data: progress
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get user's bookmarked learning content
// @route   GET /api/learning/bookmarks
// @access  Private
exports.getBookmarks = async (req, res, next) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const skip = (pageNum - 1) * limitNum;

    const bookmarks = await UserProgress.find({
      userId: req.user.id,
      isBookmarked: true
    })
      .sort('-bookmarkedAt')
      .skip(skip)
      .limit(limitNum)
      .populate({
        path: 'learningId',
        populate: [
          { path: 'userId', select: 'username' },
          { path: 'categories', select: 'name color' }
        ]
      });

    const total = await UserProgress.countDocuments({
      userId: req.user.id,
      isBookmarked: true
    });

    res.status(200).json({
      success: true,
      count: bookmarks.length,
      pagination: {
        total,
        page: pageNum,
        limit: limitNum,
        pages: Math.ceil(total / limitNum)
      },
      data: bookmarks
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Mark learning content as completed
// @route   POST /api/learning/:id/complete
// @access  Private
exports.markAsCompleted = async (req, res, next) => {
  try {
    const learning = await Learning.findById(req.params.id);

    if (!learning) {
      return res.status(404).json({
        success: false,
        error: 'Learning content not found'
      });
    }

    let progress = await UserProgress.findOne({
      userId: req.user.id,
      learningId: req.params.id
    });

    if (progress) {
      progress.isCompleted = true;
      progress.readingProgress = 100;
      await progress.save();
    } else {
      progress = await UserProgress.create({
        userId: req.user.id,
        learningId: req.params.id,
        isCompleted: true,
        readingProgress: 100
      });
    }

    res.status(200).json({
      success: true,
      data: progress
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Update reading progress
// @route   PUT /api/learning/:id/progress
// @access  Private
exports.updateProgress = async (req, res, next) => {
  try {
    const { readingProgress } = req.body;

    if (readingProgress < 0 || readingProgress > 100) {
      return res.status(400).json({
        success: false,
        error: 'Reading progress must be between 0 and 100'
      });
    }

    const learning = await Learning.findById(req.params.id);

    if (!learning) {
      return res.status(404).json({
        success: false,
        error: 'Learning content not found'
      });
    }

    let progress = await UserProgress.findOne({
      userId: req.user.id,
      learningId: req.params.id
    });

    if (progress) {
      progress.readingProgress = readingProgress;
      if (readingProgress === 100) {
        progress.isCompleted = true;
      }
      await progress.save();
    } else {
      progress = await UserProgress.create({
        userId: req.user.id,
        learningId: req.params.id,
        readingProgress,
        isCompleted: readingProgress === 100
      });
    }

    res.status(200).json({
      success: true,
      data: progress
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get user's learning progress
// @route   GET /api/learning/progress
// @access  Private
exports.getUserProgress = async (req, res, next) => {
  try {
    const { page = 1, limit = 10, completed } = req.query;
    const pageNum = parseInt(page, 10);
    const limitNum = parseInt(limit, 10);
    const skip = (pageNum - 1) * limitNum;

    const filter = { userId: req.user.id };
    
    if (completed !== undefined) {
      filter.isCompleted = completed === 'true';
    }

    const progress = await UserProgress.find(filter)
      .sort('-updatedAt')
      .skip(skip)
      .limit(limitNum)
      .populate({
        path: 'learningId',
        populate: [
          { path: 'userId', select: 'username' },
          { path: 'categories', select: 'name color' }
        ]
      });

    const total = await UserProgress.countDocuments(filter);

    const stats = await UserProgress.aggregate([
      { $match: { userId: req.user._id } },
      {
        $group: {
          _id: null,
          totalStarted: { $sum: 1 },
          totalCompleted: {
            $sum: { $cond: [{ $eq: ['$isCompleted', true] }, 1, 0] }
          },
          totalBookmarked: {
            $sum: { $cond: [{ $eq: ['$isBookmarked', true] }, 1, 0] }
          },
          averageProgress: { $avg: '$readingProgress' }
        }
      }
    ]);

    res.status(200).json({
      success: true,
      count: progress.length,
      pagination: {
        total,
        page: pageNum,
        limit: limitNum,
        pages: Math.ceil(total / limitNum)
      },
      stats: stats[0] || {
        totalStarted: 0,
        totalCompleted: 0,
        totalBookmarked: 0,
        averageProgress: 0
      },
      data: progress
    });
  } catch (err) {
    next(err);
  }
};
