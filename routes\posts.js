const express = require('express');
const {
  getAllPosts,
  getPostById,
  createPost,
  updatePost,
  deletePost,
  getPostImage
} = require('../controllers/postController');
const { protect, checkOwnership } = require('../middleware/auth');
const Post = require('../models/Post');
const { upload } = require('../utils/imageUpload');

// Include comment routes
const commentRouter = require('./comments');

const router = express.Router();

// Re-route into comment router
router.use('/:id/comments', commentRouter);

// Image routes
router.get('/image/:filename', getPostImage);

router.route('/')
  .get(getAllPosts)
  .post(protect, upload.single('image'), createPost);

router.route('/:id')
  .get(getPostById)
  .put(protect, checkOwnership(Post), upload.single('image'), updatePost)
  .delete(protect, checkOwnership(Post), deletePost);

module.exports = router;
