const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const rateLimit = require('express-rate-limit');
const compression = require('compression');
const errorHandler = require('./middleware/error');
const config = require('./config/config');
const logger = require('./utils/logger');

// Route files
const authRoutes = require('./routes/auth');
const postRoutes = require('./routes/posts');
const accountRoutes = require('./routes/account');
const categoryRoutes = require('./routes/categories');
const learningRoutes = require('./routes/learning');

const app = express();

// Trust proxy for Railway deployment
app.set('trust proxy', 1);

// Request logging middleware
app.use(logger.request.bind(logger));

// Security headers
const helmetConfig = {
  contentSecurityPolicy: config.security.helmetContentSecurityPolicy
};
app.use(helmet(helmetConfig));

// Rate limiting
const limiter = rateLimit({
  windowMs: config.rateLimit.windowMs,
  max: config.rateLimit.max,
  standardHeaders: true,
  legacyHeaders: false,
  message: {
    success: false,
    error: `Too many requests from this IP, please try again after ${Math.round(config.rateLimit.windowMs / 60000)} minutes`
  }
});

// Apply rate limiting to all API routes
app.use('/api', limiter);

// Enable CORS
app.use(cors({
  origin: config.cors.origin,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: true
}));

// Compress responses
app.use(compression());

// Body parser
app.use(express.json({ limit: '10kb' })); // Limit body size
app.use(express.urlencoded({ extended: true, limit: '10kb' }));

// Add request ID middleware
app.use((req, res, next) => {
  req.id = `req-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  next();
});

// Serve the main documentation page at root FIRST
app.get('/', (req, res) => {
  res.sendFile('index.html', { root: './public' });
});

// Documentation routes
app.get('/docs', (req, res) => {
  res.sendFile('docs.html', { root: './public' });
});

app.get('/api-docs', (req, res) => {
  res.sendFile('api-docs.html', { root: './public' });
});

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(200).json({
    status: 'UP',
    environment: config.server.env,
    timestamp: new Date().toISOString()
  });
});

// Favicon route to prevent 404 warnings
app.get('/favicon.ico', (req, res) => {
  res.status(204).end();
});

// API info endpoint
app.get('/api', (req, res) => {
  res.json({
    success: true,
    message: 'API is running',
    version: process.env.npm_package_version || '1.0.1',
    environment: config.server.env,
    endpoints: {
      auth: '/api/auth',
      posts: '/api/posts',
      account: '/api/account',
      categories: '/api/categories',
      learning: '/api/learning',
      health: '/health',
      docs: '/docs'
    }
  });
});

// API status endpoint (JSON response)
app.get('/api/status', (req, res) => {
  res.json({
    success: true,
    message: 'API is running',
    version: process.env.npm_package_version || '1.0.1',
    environment: config.server.env,
    timestamp: new Date().toISOString()
  });
});

// Mount routers
app.use('/api/auth', authRoutes);
app.use('/api/posts', postRoutes);
app.use('/api/account', accountRoutes);
app.use('/api/categories', categoryRoutes);
app.use('/api/learning', learningRoutes);

// Serve static files AFTER specific routes
app.use(express.static('public'));

// 404 handler for undefined routes
app.use('*', (req, res) => {
  logger.warn(`Route not found: ${req.originalUrl}`, {
    method: req.method,
    ip: req.ip
  });

  res.status(404).json({
    success: false,
    error: `Route ${req.originalUrl} not found`
  });
});

// Error handler middleware
app.use(errorHandler);

module.exports = app;



