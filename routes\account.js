const express = require('express');
const {
  getAccount,
  updateAccount,
  deleteAccount,
  uploadProfilePicture,
  getProfilePicture,
  deleteProfilePicture
} = require('../controllers/accountController');
const { protect } = require('../middleware/auth');
const { upload } = require('../utils/imageUpload');

const router = express.Router();

router.route('/')
  .get(protect, getAccount)
  .put(protect, updateAccount)
  .delete(protect, deleteAccount);

router.route('/profile-picture')
  .post(protect, upload.single('profilePicture'), uploadProfilePicture)
  .delete(protect, deleteProfilePicture);

router.route('/profile-picture/:filename')
  .get(getProfilePicture);

module.exports = router;
