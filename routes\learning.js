const express = require('express');
const {
  getAllLearning,
  getLearning,
  createLearning,
  updateLearning,
  deleteLearning,
  getLearningImage,
  getLearningByCategory
} = require('../controllers/learningController');
const {
  bookmarkLearning,
  removeBookmark,
  getBookmarks,
  markAsCompleted,
  updateProgress,
  getUserProgress
} = require('../controllers/userProgressController');
const { protect, checkOwnership } = require('../middleware/auth');
const Learning = require('../models/Learning');
const { upload } = require('../utils/imageUpload');

const router = express.Router();

// Image routes
router.get('/image/:filename', getLearningImage);

// Progress and bookmark routes
router.get('/bookmarks', protect, getBookmarks);
router.get('/progress', protect, getUserProgress);

// Category-specific routes
router.get('/category/:categoryId', getLearningByCategory);

// Main learning routes
router.route('/')
  .get(getAllLearning)
  .post(protect, upload.single('image'), createLearning);

router.route('/:id')
  .get(getLearning)
  .put(protect, checkOwnership(Learning), upload.single('image'), updateLearning)
  .delete(protect, checkOwnership(Learning), deleteLearning);

// Progress tracking routes
router.route('/:id/bookmark')
  .post(protect, bookmarkLearning)
  .delete(protect, removeBookmark);

router.post('/:id/complete', protect, markAsCompleted);
router.put('/:id/progress', protect, updateProgress);

module.exports = router;
