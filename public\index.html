<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>API Test Page</title>
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/style.css">
  <style>
    .nav-tabs {
      margin-bottom: 20px;
    }
  </style>
</head>
<body>
  <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
    <div class="container">
      <a class="navbar-brand" href="/">API Project</a>
      <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
        <span class="navbar-toggler-icon"></span>
      </button>
      <div class="collapse navbar-collapse" id="navbarNav">
        <ul class="navbar-nav">
          <li class="nav-item">
            <a class="nav-link active" href="/">Test Page</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/api-docs.html">API Documentation</a>
          </li>
          <li class="nav-item">
            <a class="nav-link" href="/docs.html">Deployment Guide</a>
          </li>
        </ul>
      </div>
    </div>
  </nav>

  <div class="container mt-4">
    <div class="text-center mb-5">
      <h1 class="display-4 text-white fw-bold mb-3">🚀 API Test Page</h1>
      <p class="lead text-white-50">Test all API endpoints with this interactive interface</p>
    </div>

    <div class="card mb-4">
      <div class="card-header">
        <h2 class="h5 mb-0">🔐 Authentication</h2>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <div class="mb-3">
              <h3 class="h6 text-primary mb-3">📝 Register New Account</h3>
              <form id="registerForm">
                <div class="mb-3">
                  <label for="registerUsername" class="form-label">Username</label>
                  <input type="text" class="form-control" id="registerUsername" placeholder="Enter username" required>
                </div>
                <div class="mb-3">
                  <label for="registerEmail" class="form-label">Email</label>
                  <input type="email" class="form-control" id="registerEmail" placeholder="Enter email" required>
                </div>
                <div class="mb-3">
                  <label for="registerPassword" class="form-label">Password</label>
                  <input type="password" class="form-control" id="registerPassword" placeholder="Enter password" required>
                </div>
                <button type="submit" class="btn btn-primary">Register Account</button>
              </form>
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-3">
              <h3 class="h6 text-success mb-3">🔑 Login to Account</h3>
              <form id="loginForm">
                <div class="mb-3">
                  <label for="loginEmail" class="form-label">Email</label>
                  <input type="email" class="form-control" id="loginEmail" placeholder="Enter email" required>
                </div>
                <div class="mb-3">
                  <label for="loginPassword" class="form-label">Password</label>
                  <input type="password" class="form-control" id="loginPassword" placeholder="Enter password" required>
                </div>
                <button type="submit" class="btn btn-success">Login</button>
              </form>
            </div>
          </div>
        </div>

        <div class="text-center border-top pt-3">
          <h3 class="h6 text-info mb-3">👤 User Actions</h3>
          <button id="getUserBtn" class="btn btn-info">Get User Info</button>
          <button id="logoutBtn" class="btn btn-warning">Logout</button>
        </div>
      </div>
    </div>

    <div class="card mb-4">
      <div class="card-header">
        <h2 class="h5 mb-0">📝 Posts</h2>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <div class="mb-3">
              <h3 class="h6 text-primary mb-3">✍️ Create New Post</h3>
              <form id="createPostForm" enctype="multipart/form-data">
                <div class="mb-3">
                  <label for="postTitle" class="form-label">Post Title</label>
                  <input type="text" class="form-control" id="postTitle" placeholder="Enter post title" required>
                </div>
                <div class="mb-3">
                  <label for="postContent" class="form-label">Content</label>
                  <textarea class="form-control" id="postContent" placeholder="Write your post content..." rows="4" required></textarea>
                </div>
                <div class="mb-3">
                  <label for="postImage" class="form-label">📷 Image (optional)</label>
                  <input type="file" class="form-control" id="postImage" name="image" accept="image/*">
                  <small class="text-muted">Supported formats: JPG, PNG, GIF</small>
                </div>
                <button type="submit" class="btn btn-primary">Create Post</button>
              </form>
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-3">
              <h3 class="h6 text-info mb-3">📋 Manage Posts</h3>
              <div class="d-grid gap-2">
                <button id="getPostsBtn" class="btn btn-info">📄 Get All Posts</button>
                <small class="text-muted">Click to load all posts from the database</small>
              </div>
            </div>
          </div>
        </div>

        <div class="mt-4">
          <h3 class="h6 text-secondary mb-3">📚 Posts List</h3>
          <div id="postsList" class="list-group"></div>
        </div>
      </div>
    </div>

    <div class="card mb-4">
      <div class="card-header">
        <h2 class="h5 mb-0">💬 Comments</h2>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <div class="mb-3">
              <h3 class="h6 text-primary mb-3">✍️ Add New Comment</h3>
              <form id="addCommentForm">
                <div class="mb-3">
                  <label for="postIdForComment" class="form-label">Post ID</label>
                  <input type="text" class="form-control" id="postIdForComment" placeholder="Enter post ID" required>
                  <small class="text-muted">Copy the post ID from the posts list above</small>
                </div>
                <div class="mb-3">
                  <label for="commentContent" class="form-label">Comment</label>
                  <textarea class="form-control" id="commentContent" placeholder="Write your comment..." rows="3" required></textarea>
                </div>
                <button type="submit" class="btn btn-primary">Add Comment</button>
              </form>
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-3">
              <h3 class="h6 text-info mb-3">📋 View Comments</h3>
              <div class="input-group mb-3">
                <input type="text" class="form-control" id="postIdForGetComments" placeholder="Enter post ID">
                <button id="getCommentsBtn" class="btn btn-info">Get Comments</button>
              </div>
              <small class="text-muted">Enter a post ID to view all comments for that post</small>
            </div>
          </div>
        </div>

        <div class="mt-4">
          <h3 class="h6 text-secondary mb-3">💭 Comments List</h3>
          <div id="commentsList" class="list-group"></div>
        </div>
      </div>
    </div>

    <div class="card mb-4">
      <div class="card-header">
        <h2 class="h5 mb-0">Categories</h2>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <h3 class="h6">Create Category</h3>
          <form id="createCategoryForm">
            <div class="mb-2">
              <input type="text" class="form-control" id="categoryName" placeholder="Category Name" required>
            </div>
            <div class="mb-2">
              <textarea class="form-control" id="categoryDescription" placeholder="Description"></textarea>
            </div>
            <div class="mb-2">
              <input type="color" class="form-control" id="categoryColor" value="#3B82F6">
            </div>
            <div class="mb-2">
              <input type="text" class="form-control" id="categoryIcon" placeholder="Icon (e.g., book, code)">
            </div>
            <button type="submit" class="btn btn-primary">Create Category</button>
          </form>
        </div>

        <div>
          <h3 class="h6">Get Categories</h3>
          <button id="getCategoriesBtn" class="btn btn-info">Get All Categories</button>
        </div>

        <div class="mt-3">
          <h3 class="h6">Categories List</h3>
          <div id="categoriesList" class="list-group"></div>
        </div>
      </div>
    </div>

    <div class="card mb-4">
      <div class="card-header">
        <h2 class="h5 mb-0">Learning Content</h2>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <h3 class="h6">Create Learning Content</h3>
          <form id="createLearningForm" enctype="multipart/form-data">
            <div class="mb-2">
              <input type="text" class="form-control" id="learningTitle" placeholder="Title" required>
            </div>
            <div class="mb-2">
              <textarea class="form-control" id="learningContent" placeholder="Content (long text)" rows="5" required></textarea>
            </div>
            <div class="mb-2">
              <textarea class="form-control" id="learningSummary" placeholder="Summary" rows="2"></textarea>
            </div>
            <div class="mb-2">
              <select class="form-control" id="learningDifficulty">
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>
            </div>
            <div class="mb-2">
              <input type="number" class="form-control" id="learningEstimatedTime" placeholder="Estimated Time (minutes)" min="1" max="300">
            </div>
            <div class="mb-2">
              <select class="form-control" id="learningCategories" multiple>
                <option value="">Select categories...</option>
              </select>
              <small class="text-muted">Hold Ctrl/Cmd to select multiple categories</small>
            </div>
            <div class="mb-2">
              <label for="learningImage" class="form-label">Image (optional)</label>
              <input type="file" class="form-control" id="learningImage" name="image" accept="image/*">
            </div>
            <div class="mb-2">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="learningPublished" checked>
                <label class="form-check-label" for="learningPublished">
                  Published
                </label>
              </div>
            </div>
            <button type="submit" class="btn btn-primary">Create Learning Content</button>
          </form>
        </div>

        <div class="mb-3">
          <h3 class="h6">Filter Learning Content</h3>
          <div class="row">
            <div class="col-md-3">
              <select class="form-control" id="filterCategory">
                <option value="">All Categories</option>
              </select>
            </div>
            <div class="col-md-3">
              <select class="form-control" id="filterDifficulty">
                <option value="">All Difficulties</option>
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>
            </div>
            <div class="col-md-4">
              <input type="text" class="form-control" id="searchKeyword" placeholder="Search keyword">
            </div>
            <div class="col-md-2">
              <button id="getLearningBtn" class="btn btn-info w-100">Get Learning</button>
            </div>
          </div>
        </div>

        <div class="mt-3">
          <h3 class="h6">Learning Content List</h3>
          <div id="learningList" class="list-group"></div>
        </div>
      </div>
    </div>

    <div class="card mb-4">
      <div class="card-header">
        <h2 class="h5 mb-0">Learning Progress</h2>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <h3 class="h6">Progress Actions</h3>
          <div class="row">
            <div class="col-md-4">
              <input type="text" class="form-control" id="learningIdForProgress" placeholder="Learning Content ID">
            </div>
            <div class="col-md-8">
              <button id="bookmarkBtn" class="btn btn-success btn-sm">Bookmark</button>
              <button id="removeBookmarkBtn" class="btn btn-warning btn-sm">Remove Bookmark</button>
              <button id="markCompleteBtn" class="btn btn-primary btn-sm">Mark Complete</button>
              <button id="updateProgressBtn" class="btn btn-info btn-sm">Update Progress</button>
            </div>
          </div>
        </div>

        <div class="mb-3">
          <h3 class="h6">My Progress</h3>
          <div class="row">
            <div class="col-md-6">
              <button id="getBookmarksBtn" class="btn btn-info">Get Bookmarks</button>
            </div>
            <div class="col-md-6">
              <button id="getProgressBtn" class="btn btn-info">Get Progress</button>
            </div>
          </div>
        </div>

        <div class="mt-3">
          <h3 class="h6">Progress List</h3>
          <div id="progressList" class="list-group"></div>
        </div>
      </div>
    </div>

    <div class="card">
      <div class="card-header">
        <h2 class="h5 mb-0">Account</h2>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <h3 class="h6">Account Details</h3>
          <button id="getAccountBtn" class="btn btn-info">Get Account</button>
          <div id="accountDetails" class="mt-2"></div>
        </div>

        <div class="mb-3">
          <h3 class="h6">Profile Picture</h3>
          <div id="profilePictureSection" class="mb-3">
            <div id="currentProfilePicture" class="mb-2"></div>
            <form id="uploadProfilePictureForm" enctype="multipart/form-data">
              <div class="mb-2">
                <label for="profilePictureFile" class="form-label">Upload Profile Picture</label>
                <input type="file" class="form-control" id="profilePictureFile" name="profilePicture" accept="image/*" required>
                <small class="text-muted">Accepted formats: JPG, JPEG, PNG, GIF (Max: 5MB)</small>
              </div>
              <button type="submit" class="btn btn-primary">Upload</button>
            </form>
            <button id="deleteProfilePictureBtn" class="btn btn-danger mt-2" style="display: none;">Delete Profile Picture</button>
          </div>
        </div>

        <div class="mb-3">
          <h3 class="h6">Update Account</h3>
          <form id="updateAccountForm">
            <div class="mb-2">
              <input type="text" class="form-control" id="updateUsername" placeholder="New Username">
            </div>
            <div class="mb-2">
              <input type="email" class="form-control" id="updateEmail" placeholder="New Email">
            </div>
            <button type="submit" class="btn btn-primary">Update</button>
          </form>
        </div>

        <div>
          <h3 class="h6">Delete Account</h3>
          <button id="deleteAccountBtn" class="btn btn-danger">Delete Account</button>
        </div>
      </div>
    </div>

    <div class="card mt-4">
      <div class="card-header">
        <h2 class="h5 mb-0">📊 API Response</h2>
      </div>
      <div class="card-body">
        <pre id="response" class="bg-light p-3 rounded"></pre>
        <small class="text-muted">API responses will appear here in real-time</small>
      </div>
    </div>
  </div>

  <footer class="bg-dark text-white mt-5 py-3">
    <div class="container text-center">
      <p class="mb-0">API Project &copy; 2023</p>
    </div>
  </footer>

  <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
  <script src="js/script.js"></script>
</body>
</html>
