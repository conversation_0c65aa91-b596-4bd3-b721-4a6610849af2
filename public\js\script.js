document.addEventListener('DOMContentLoaded', function() {
  // API base URL
  const API_URL = '/api';

  // DOM elements
  const responseElement = document.getElementById('response');
  const postsListElement = document.getElementById('postsList');
  const commentsListElement = document.getElementById('commentsList');
  const accountDetailsElement = document.getElementById('accountDetails');
  const currentProfilePictureElement = document.getElementById('currentProfilePicture');
  const deleteProfilePictureBtn = document.getElementById('deleteProfilePictureBtn');
  const categoriesListElement = document.getElementById('categoriesList');
  const learningListElement = document.getElementById('learningList');
  const progressListElement = document.getElementById('progressList');

  // Token storage
  let token = localStorage.getItem('token');

  // Helper function to display response
  function displayResponse(data) {
    responseElement.textContent = JSON.stringify(data, null, 2);
  }

  // Helper function to display profile picture
  function displayProfilePicture(profilePictureUrl) {
    if (profilePictureUrl) {
      currentProfilePictureElement.innerHTML = `
        <div class="text-center">
          <img src="${profilePictureUrl}" alt="Profile Picture" class="img-thumbnail" style="max-width: 150px; max-height: 150px;">
          <p class="mt-2 text-muted">Current Profile Picture</p>
        </div>
      `;
      deleteProfilePictureBtn.style.display = 'inline-block';
    } else {
      currentProfilePictureElement.innerHTML = `
        <div class="text-center text-muted">
          <div class="border rounded p-3" style="width: 150px; height: 150px; display: flex; align-items: center; justify-content: center; margin: 0 auto;">
            <span>No Profile Picture</span>
          </div>
        </div>
      `;
      deleteProfilePictureBtn.style.display = 'none';
    }
  }

  // Helper function to show notifications
  function showNotification(message, type = 'info') {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    `;
    document.body.appendChild(alertDiv);

    // Auto remove after 5 seconds
    setTimeout(() => {
      if (alertDiv.parentNode) {
        alertDiv.remove();
      }
    }, 5000);
  }

  // Helper function to add loading state to button
  function setButtonLoading(button, loading = true) {
    if (loading) {
      button.disabled = true;
      button.classList.add('loading');
      button.dataset.originalText = button.textContent;
    } else {
      button.disabled = false;
      button.classList.remove('loading');
      if (button.dataset.originalText) {
        button.textContent = button.dataset.originalText;
      }
    }
  }

  // Helper function for API requests
  async function apiRequest(endpoint, method = 'GET', body = null) {
    const headers = {
      'Content-Type': 'application/json'
    };

    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const options = {
      method,
      headers
    };

    if (body) {
      options.body = JSON.stringify(body);
    }

    try {
      const response = await fetch(`${API_URL}${endpoint}`, options);
      const data = await response.json();

      displayResponse(data);
      return data;
    } catch (error) {
      displayResponse({ error: error.message });
      return { success: false, error: error.message };
    }
  }

  // Register form
  document.getElementById('registerForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const username = document.getElementById('registerUsername').value;
    const email = document.getElementById('registerEmail').value;
    const password = document.getElementById('registerPassword').value;

    const data = await apiRequest('/auth/register', 'POST', {
      username,
      email,
      password
    });

    if (data.success) {
      token = data.token;
      localStorage.setItem('token', token);
      alert('Registration successful!');
    }
  });

  // Login form
  document.getElementById('loginForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const email = document.getElementById('loginEmail').value;
    const password = document.getElementById('loginPassword').value;

    const data = await apiRequest('/auth/login', 'POST', {
      email,
      password
    });

    if (data.success) {
      token = data.token;
      localStorage.setItem('token', token);
      alert('Login successful!');
    }
  });

  // Get user button
  document.getElementById('getUserBtn').addEventListener('click', async function() {
    await apiRequest('/auth/user');
  });

  // Logout button
  document.getElementById('logoutBtn').addEventListener('click', async function() {
    await apiRequest('/auth/logout', 'POST');
    token = null;
    localStorage.removeItem('token');
    alert('Logged out!');
  });

  // Create post form
  document.getElementById('createPostForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const title = document.getElementById('postTitle').value;
    const content = document.getElementById('postContent').value;
    const imageFile = document.getElementById('postImage').files[0];

    // Use FormData for file uploads
    const formData = new FormData();
    formData.append('title', title);
    formData.append('content', content);

    if (imageFile) {
      formData.append('image', imageFile);
    }

    // Custom request for file upload
    const headers = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    try {
      const response = await fetch(`${API_URL}/posts`, {
        method: 'POST',
        headers,
        body: formData
      });

      const data = await response.json();
      displayResponse(data);

      if (data.success) {
        alert('Post created!');
        document.getElementById('postTitle').value = '';
        document.getElementById('postContent').value = '';
        document.getElementById('postImage').value = '';
        document.getElementById('getPostsBtn').click();
      }
    } catch (error) {
      displayResponse({ error: error.message });
    }
  });

  // Get posts button
  document.getElementById('getPostsBtn').addEventListener('click', async function() {
    const data = await apiRequest('/posts');

    if (data.success) {
      postsListElement.innerHTML = '';

      if (data.data.length === 0) {
        postsListElement.innerHTML = `
          <div class="empty-state">
            <div class="text-center">
              <i class="fas fa-file-alt" style="font-size: 3rem; color: #dee2e6; margin-bottom: 1rem;"></i>
              <h6 class="text-muted">No posts found</h6>
              <p class="text-muted">Create your first post to get started!</p>
            </div>
          </div>
        `;
        return;
      }

      data.data.forEach(post => {
        const postElement = document.createElement('div');
        postElement.className = 'list-group-item';

        // Create image HTML if post has an image
        let imageHtml = '';
        if (post.imageUrl) {
          imageHtml = `<div class="mb-3">
            <img src="${post.imageUrl}" alt="${post.title}" class="img-fluid post-image"
                 style="max-width: 100%; max-height: 250px; object-fit: cover; cursor: pointer;"
                 onclick="window.open('${post.imageUrl}', '_blank')"
                 onerror="this.onerror=null; this.src='https://via.placeholder.com/300x200?text=Image+Not+Found'; this.alt='Image not available';">
          </div>`;
        }

        postElement.innerHTML = `
          <div class="d-flex justify-content-between align-items-start mb-3">
            <h5 class="mb-0 text-primary">${post.title}</h5>
            <small class="text-muted">ID: ${post.id || post._id}</small>
          </div>
          ${imageHtml}
          <p class="text-secondary mb-3">${post.content}</p>
          <div class="d-flex gap-2 flex-wrap">
            <button class="btn btn-sm btn-info view-post" data-id="${post.id || post._id}">
              👁️ View
            </button>
            <button class="btn btn-sm btn-warning edit-post" data-id="${post.id || post._id}">
              ✏️ Edit
            </button>
            <button class="btn btn-sm btn-danger delete-post" data-id="${post.id || post._id}">
              🗑️ Delete
            </button>
          </div>
        `;
        postsListElement.appendChild(postElement);
      });

      // Add event listeners for post actions
      document.querySelectorAll('.view-post').forEach(button => {
        button.addEventListener('click', async function() {
          const postId = this.getAttribute('data-id');
          await apiRequest(`/posts/${postId}`);
        });
      });

      document.querySelectorAll('.edit-post').forEach(button => {
        button.addEventListener('click', async function() {
          const postId = this.getAttribute('data-id');

          // Create a modal for editing the post with image upload
          const modalHtml = `
            <div class="modal fade" id="editPostModal" tabindex="-1" aria-hidden="true">
              <div class="modal-dialog">
                <div class="modal-content">
                  <div class="modal-header">
                    <h5 class="modal-title">Edit Post</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                  </div>
                  <div class="modal-body">
                    <form id="editPostForm" enctype="multipart/form-data">
                      <div class="mb-3">
                        <label for="editPostTitle" class="form-label">Title</label>
                        <input type="text" class="form-control" id="editPostTitle" required>
                      </div>
                      <div class="mb-3">
                        <label for="editPostContent" class="form-label">Content</label>
                        <textarea class="form-control" id="editPostContent" rows="3" required></textarea>
                      </div>
                      <div class="mb-3">
                        <label for="editPostImage" class="form-label">Image (optional)</label>
                        <input type="file" class="form-control" id="editPostImage" name="image" accept="image/*">
                        <small class="text-muted">Leave empty to keep the current image</small>
                      </div>
                    </form>
                  </div>
                  <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-primary" id="saveEditBtn">Save Changes</button>
                  </div>
                </div>
              </div>
            </div>
          `;

          // Add modal to the document if it doesn't exist
          if (!document.getElementById('editPostModal')) {
            document.body.insertAdjacentHTML('beforeend', modalHtml);
          }

          // Get post data
          const postData = await apiRequest(`/posts/${postId}`);

          if (postData.success) {
            // Fill the form with current post data
            document.getElementById('editPostTitle').value = postData.data.title;
            document.getElementById('editPostContent').value = postData.data.content;

            // Show the modal
            const modal = new bootstrap.Modal(document.getElementById('editPostModal'));
            modal.show();

            // Handle save button click
            document.getElementById('saveEditBtn').onclick = async function() {
              const title = document.getElementById('editPostTitle').value;
              const content = document.getElementById('editPostContent').value;
              const imageFile = document.getElementById('editPostImage').files[0];

              if (!title || !content) {
                alert('Title and content are required');
                return;
              }

              // Use FormData for file uploads
              const formData = new FormData();
              formData.append('title', title);
              formData.append('content', content);

              if (imageFile) {
                formData.append('image', imageFile);
              }

              // Custom request for file upload
              const headers = {};
              if (token) {
                headers['Authorization'] = `Bearer ${token}`;
              }

              try {
                const response = await fetch(`${API_URL}/posts/${postId}`, {
                  method: 'PUT',
                  headers,
                  body: formData
                });

                const data = await response.json();
                displayResponse(data);

                if (data.success) {
                  modal.hide();
                  document.getElementById('getPostsBtn').click();
                }
              } catch (error) {
                displayResponse({ error: error.message });
              }
            };
          }
        });
      });

      document.querySelectorAll('.delete-post').forEach(button => {
        button.addEventListener('click', async function() {
          const postId = this.getAttribute('data-id');
          if (confirm('Are you sure you want to delete this post?')) {
            await apiRequest(`/posts/${postId}`, 'DELETE');
            document.getElementById('getPostsBtn').click();
          }
        });
      });
    }
  });

  // Add comment form
  document.getElementById('addCommentForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const postId = document.getElementById('postIdForComment').value;
    const content = document.getElementById('commentContent').value;

    const data = await apiRequest(`/posts/${postId}/comments`, 'POST', {
      content
    });

    if (data.success) {
      alert('Comment added!');
      document.getElementById('postIdForGetComments').value = postId;
      document.getElementById('getCommentsBtn').click();
    }
  });

  // Get comments button
  document.getElementById('getCommentsBtn').addEventListener('click', async function() {
    const postId = document.getElementById('postIdForGetComments').value;

    if (!postId) {
      alert('Please enter a Post ID');
      return;
    }

    const data = await apiRequest(`/posts/${postId}/comments`);

    if (data.success) {
      commentsListElement.innerHTML = '';

      data.data.forEach(comment => {
        const commentElement = document.createElement('div');
        commentElement.className = 'list-group-item';
        commentElement.innerHTML = `
          <p>${comment.content}</p>
          <small>ID: ${comment._id}</small>
          <div class="mt-2">
            <button class="btn btn-sm btn-warning edit-comment" data-post-id="${postId}" data-id="${comment._id}">Edit</button>
            <button class="btn btn-sm btn-danger delete-comment" data-post-id="${postId}" data-id="${comment._id}">Delete</button>
          </div>
        `;
        commentsListElement.appendChild(commentElement);
      });

      // Add event listeners for comment actions
      document.querySelectorAll('.edit-comment').forEach(button => {
        button.addEventListener('click', async function() {
          const postId = this.getAttribute('data-post-id');
          const commentId = this.getAttribute('data-id');
          const newContent = prompt('Enter new comment:');

          if (newContent) {
            await apiRequest(`/posts/${postId}/comments/${commentId}`, 'PUT', {
              content: newContent
            });
            document.getElementById('getCommentsBtn').click();
          }
        });
      });

      document.querySelectorAll('.delete-comment').forEach(button => {
        button.addEventListener('click', async function() {
          const postId = this.getAttribute('data-post-id');
          const commentId = this.getAttribute('data-id');

          if (confirm('Are you sure you want to delete this comment?')) {
            await apiRequest(`/posts/${postId}/comments/${commentId}`, 'DELETE');
            document.getElementById('getCommentsBtn').click();
          }
        });
      });
    }
  });

  // Get account button
  document.getElementById('getAccountBtn').addEventListener('click', async function() {
    const data = await apiRequest('/account');

    if (data.success) {
      accountDetailsElement.innerHTML = `
        <div class="bg-light p-3 rounded">
          <p><strong>Username:</strong> ${data.data.username}</p>
          <p><strong>Email:</strong> ${data.data.email}</p>
          <p><strong>Profile Picture URL:</strong> ${data.data.profilePictureUrl || 'None'}</p>
          <p class="mb-0"><strong>Created At:</strong> ${new Date(data.data.createdAt).toLocaleString()}</p>
        </div>
      `;

      // Display profile picture
      displayProfilePicture(data.data.profilePictureUrl);
    }
  });

  // Upload profile picture form
  document.getElementById('uploadProfilePictureForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const fileInput = document.getElementById('profilePictureFile');
    const file = fileInput.files[0];

    if (!file) {
      alert('Please select a file');
      return;
    }

    // Check file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      alert('File size must be less than 5MB');
      return;
    }

    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
      alert('Only JPG, JPEG, PNG, and GIF files are allowed');
      return;
    }

    const formData = new FormData();
    formData.append('profilePicture', file);

    const headers = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    try {
      const response = await fetch(`${API_URL}/account/profile-picture`, {
        method: 'POST',
        headers,
        body: formData
      });

      const data = await response.json();
      displayResponse(data);

      if (data.success) {
        alert('Profile picture uploaded successfully!');
        fileInput.value = '';
        displayProfilePicture(data.data.profilePictureUrl);
        // Refresh account details
        document.getElementById('getAccountBtn').click();
      }
    } catch (error) {
      displayResponse({ error: error.message });
    }
  });

  // Delete profile picture button
  deleteProfilePictureBtn.addEventListener('click', async function() {
    if (confirm('Are you sure you want to delete your profile picture?')) {
      const data = await apiRequest('/account/profile-picture', 'DELETE');

      if (data.success) {
        alert('Profile picture deleted successfully!');
        displayProfilePicture(null);
        // Refresh account details
        document.getElementById('getAccountBtn').click();
      }
    }
  });

  // Update account form
  document.getElementById('updateAccountForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const username = document.getElementById('updateUsername').value;
    const email = document.getElementById('updateEmail').value;

    const data = await apiRequest('/account', 'PUT', {
      username,
      email
    });

    if (data.success) {
      alert('Account updated!');
      document.getElementById('getAccountBtn').click();
    }
  });

  // Delete account button
  document.getElementById('deleteAccountBtn').addEventListener('click', async function() {
    if (confirm('Are you sure you want to delete your account? This action cannot be undone!')) {
      const data = await apiRequest('/account', 'DELETE');

      if (data.success) {
        token = null;
        localStorage.removeItem('token');
        alert('Account deleted!');
      }
    }
  });

  // ===== LEARNING MODULE FUNCTIONALITY =====

  // Helper function to load categories into select elements
  async function loadCategories() {
    const data = await apiRequest('/categories');
    if (data.success) {
      const learningCategoriesSelect = document.getElementById('learningCategories');
      const filterCategorySelect = document.getElementById('filterCategory');

      // Clear existing options
      learningCategoriesSelect.innerHTML = '<option value="">Select categories...</option>';
      filterCategorySelect.innerHTML = '<option value="">All Categories</option>';

      data.data.forEach(category => {
        const option1 = new Option(category.name, category.id);
        const option2 = new Option(category.name, category.id);
        learningCategoriesSelect.appendChild(option1);
        filterCategorySelect.appendChild(option2);
      });
    }
  }

  // Create category form
  document.getElementById('createCategoryForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const name = document.getElementById('categoryName').value;
    const description = document.getElementById('categoryDescription').value;
    const color = document.getElementById('categoryColor').value;
    const icon = document.getElementById('categoryIcon').value;

    const data = await apiRequest('/categories', 'POST', {
      name,
      description,
      color,
      icon
    });

    if (data.success) {
      alert('Category created!');
      document.getElementById('createCategoryForm').reset();
      document.getElementById('getCategoriesBtn').click();
      loadCategories(); // Reload categories in select elements
    }
  });

  // Get categories button
  document.getElementById('getCategoriesBtn').addEventListener('click', async function() {
    const data = await apiRequest('/categories');

    if (data.success) {
      categoriesListElement.innerHTML = '';

      data.data.forEach(category => {
        const categoryElement = document.createElement('div');
        categoryElement.className = 'list-group-item';
        categoryElement.innerHTML = `
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h6 class="mb-1" style="color: ${category.color}">
                <i class="fas fa-${category.icon || 'book'}"></i> ${category.name}
              </h6>
              <p class="mb-1">${category.description || 'No description'}</p>
              <small>ID: ${category.id}</small>
            </div>
            <div>
              <button class="btn btn-sm btn-warning edit-category" data-id="${category.id}">Edit</button>
              <button class="btn btn-sm btn-danger delete-category" data-id="${category.id}">Delete</button>
            </div>
          </div>
        `;
        categoriesListElement.appendChild(categoryElement);
      });

      // Add event listeners for category actions
      document.querySelectorAll('.edit-category').forEach(button => {
        button.addEventListener('click', async function() {
          const categoryId = this.getAttribute('data-id');
          const newName = prompt('Enter new category name:');
          if (newName) {
            await apiRequest(`/categories/${categoryId}`, 'PUT', { name: newName });
            document.getElementById('getCategoriesBtn').click();
            loadCategories();
          }
        });
      });

      document.querySelectorAll('.delete-category').forEach(button => {
        button.addEventListener('click', async function() {
          const categoryId = this.getAttribute('data-id');
          if (confirm('Are you sure you want to delete this category?')) {
            await apiRequest(`/categories/${categoryId}`, 'DELETE');
            document.getElementById('getCategoriesBtn').click();
            loadCategories();
          }
        });
      });
    }
  });

  // Create learning content form
  document.getElementById('createLearningForm').addEventListener('submit', async function(e) {
    e.preventDefault();

    const title = document.getElementById('learningTitle').value;
    const content = document.getElementById('learningContent').value;
    const summary = document.getElementById('learningSummary').value;
    const difficulty = document.getElementById('learningDifficulty').value;
    const estimatedTime = document.getElementById('learningEstimatedTime').value;
    const isPublished = document.getElementById('learningPublished').checked;
    const imageFile = document.getElementById('learningImage').files[0];

    // Get selected categories
    const categoriesSelect = document.getElementById('learningCategories');
    const selectedCategories = Array.from(categoriesSelect.selectedOptions)
      .map(option => option.value)
      .filter(value => value !== '');

    // Use FormData for file uploads
    const formData = new FormData();
    formData.append('title', title);
    formData.append('content', content);
    formData.append('summary', summary);
    formData.append('difficulty', difficulty);
    formData.append('estimatedTime', estimatedTime);
    formData.append('isPublished', isPublished);
    formData.append('categories', JSON.stringify(selectedCategories));

    if (imageFile) {
      formData.append('image', imageFile);
    }

    // Custom request for file upload
    const headers = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    try {
      const response = await fetch(`${API_URL}/learning`, {
        method: 'POST',
        headers,
        body: formData
      });

      const data = await response.json();
      displayResponse(data);

      if (data.success) {
        alert('Learning content created!');
        document.getElementById('createLearningForm').reset();
        document.getElementById('getLearningBtn').click();
      }
    } catch (error) {
      displayResponse({ error: error.message });
    }
  });

  // Get learning content button
  document.getElementById('getLearningBtn').addEventListener('click', async function() {
    const category = document.getElementById('filterCategory').value;
    const difficulty = document.getElementById('filterDifficulty').value;
    const search = document.getElementById('searchKeyword').value;

    let endpoint = '/learning?';
    const params = [];

    if (category) params.push(`category=${category}`);
    if (difficulty) params.push(`difficulty=${difficulty}`);
    if (search) params.push(`search=${encodeURIComponent(search)}`);

    endpoint += params.join('&');

    const data = await apiRequest(endpoint);

    if (data.success) {
      learningListElement.innerHTML = '';

      data.data.forEach(learning => {
        const learningElement = document.createElement('div');
        learningElement.className = 'list-group-item';

        // Create image HTML if learning has an image
        let imageHtml = '';
        if (learning.imageUrl) {
          imageHtml = `<div class="mb-3">
            <img src="${learning.imageUrl}" alt="${learning.title}" class="img-fluid rounded" style="max-height: 150px;"
                 onerror="this.onerror=null; this.src='https://via.placeholder.com/300x150?text=Image+Not+Found'; this.alt='Image not available';">
          </div>`;
        }

        // Create categories HTML
        let categoriesHtml = '';
        if (learning.categories && learning.categories.length > 0) {
          categoriesHtml = learning.categories.map(cat =>
            `<span class="badge" style="background-color: ${cat.color}">${cat.name}</span>`
          ).join(' ');
        }

        learningElement.innerHTML = `
          <div>
            <div class="d-flex justify-content-between align-items-start">
              <div class="flex-grow-1">
                <h5>${learning.title}</h5>
                ${imageHtml}
                <p class="mb-2">${learning.summary || learning.content.substring(0, 150) + '...'}</p>
                <div class="mb-2">
                  <small class="text-muted">
                    <strong>Difficulty:</strong> ${learning.difficulty} |
                    <strong>Time:</strong> ${learning.estimatedTime} min |
                    <strong>Views:</strong> ${learning.viewCount}
                  </small>
                </div>
                <div class="mb-2">${categoriesHtml}</div>
                <small>ID: ${learning.id}</small>
              </div>
              <div class="ms-3">
                <button class="btn btn-sm btn-info view-learning" data-id="${learning.id}">View</button>
                <button class="btn btn-sm btn-warning edit-learning" data-id="${learning.id}">Edit</button>
                <button class="btn btn-sm btn-danger delete-learning" data-id="${learning.id}">Delete</button>
              </div>
            </div>
          </div>
        `;
        learningListElement.appendChild(learningElement);
      });

      // Add event listeners for learning actions
      document.querySelectorAll('.view-learning').forEach(button => {
        button.addEventListener('click', async function() {
          const learningId = this.getAttribute('data-id');
          await apiRequest(`/learning/${learningId}`);
        });
      });

      document.querySelectorAll('.edit-learning').forEach(button => {
        button.addEventListener('click', async function() {
          const learningId = this.getAttribute('data-id');
          alert('Edit functionality would open a modal similar to posts. Implementation can be added based on needs.');
        });
      });

      document.querySelectorAll('.delete-learning').forEach(button => {
        button.addEventListener('click', async function() {
          const learningId = this.getAttribute('data-id');
          if (confirm('Are you sure you want to delete this learning content?')) {
            await apiRequest(`/learning/${learningId}`, 'DELETE');
            document.getElementById('getLearningBtn').click();
          }
        });
      });
    }
  });

  // Progress functionality
  document.getElementById('bookmarkBtn').addEventListener('click', async function() {
    const learningId = document.getElementById('learningIdForProgress').value;
    if (!learningId) {
      alert('Please enter a Learning Content ID');
      return;
    }
    await apiRequest(`/learning/${learningId}/bookmark`, 'POST');
  });

  document.getElementById('removeBookmarkBtn').addEventListener('click', async function() {
    const learningId = document.getElementById('learningIdForProgress').value;
    if (!learningId) {
      alert('Please enter a Learning Content ID');
      return;
    }
    await apiRequest(`/learning/${learningId}/bookmark`, 'DELETE');
  });

  document.getElementById('markCompleteBtn').addEventListener('click', async function() {
    const learningId = document.getElementById('learningIdForProgress').value;
    if (!learningId) {
      alert('Please enter a Learning Content ID');
      return;
    }
    await apiRequest(`/learning/${learningId}/complete`, 'POST');
  });

  document.getElementById('updateProgressBtn').addEventListener('click', async function() {
    const learningId = document.getElementById('learningIdForProgress').value;
    if (!learningId) {
      alert('Please enter a Learning Content ID');
      return;
    }
    const progress = prompt('Enter reading progress (0-100):');
    if (progress !== null && !isNaN(progress) && progress >= 0 && progress <= 100) {
      await apiRequest(`/learning/${learningId}/progress`, 'PUT', {
        readingProgress: parseInt(progress)
      });
    }
  });

  document.getElementById('getBookmarksBtn').addEventListener('click', async function() {
    const data = await apiRequest('/learning/bookmarks');

    if (data.success) {
      progressListElement.innerHTML = '<h6>Bookmarked Content:</h6>';

      data.data.forEach(bookmark => {
        const learning = bookmark.learningId;
        const progressElement = document.createElement('div');
        progressElement.className = 'list-group-item';
        progressElement.innerHTML = `
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h6 class="mb-1">${learning.title}</h6>
              <p class="mb-1">${learning.summary || 'No summary'}</p>
              <small>Bookmarked: ${new Date(bookmark.bookmarkedAt).toLocaleString()}</small>
            </div>
            <span class="badge bg-success">Bookmarked</span>
          </div>
        `;
        progressListElement.appendChild(progressElement);
      });
    }
  });

  document.getElementById('getProgressBtn').addEventListener('click', async function() {
    const data = await apiRequest('/learning/progress');

    if (data.success) {
      progressListElement.innerHTML = `
        <h6>Learning Progress:</h6>
        <div class="mb-3">
          <div class="row">
            <div class="col-md-3">
              <div class="bg-primary text-white text-center p-3 rounded">
                <h5 class="mb-1">${data.stats.totalStarted}</h5>
                <small>Started</small>
              </div>
            </div>
            <div class="col-md-3">
              <div class="bg-success text-white text-center p-3 rounded">
                <h5 class="mb-1">${data.stats.totalCompleted}</h5>
                <small>Completed</small>
              </div>
            </div>
            <div class="col-md-3">
              <div class="bg-warning text-white text-center p-3 rounded">
                <h5 class="mb-1">${data.stats.totalBookmarked}</h5>
                <small>Bookmarked</small>
              </div>
            </div>
            <div class="col-md-3">
              <div class="bg-info text-white text-center p-3 rounded">
                <h5 class="mb-1">${Math.round(data.stats.averageProgress)}%</h5>
                <small>Avg Progress</small>
              </div>
            </div>
          </div>
        </div>
      `;

      data.data.forEach(progress => {
        const learning = progress.learningId;
        const progressElement = document.createElement('div');
        progressElement.className = 'list-group-item';
        progressElement.innerHTML = `
          <div class="d-flex justify-content-between align-items-center">
            <div>
              <h6 class="mb-1">${learning.title}</h6>
              <div class="progress mb-2" style="height: 10px;">
                <div class="progress-bar" role="progressbar" style="width: ${progress.readingProgress}%"></div>
              </div>
              <small>Progress: ${progress.readingProgress}% |
                ${progress.isCompleted ? 'Completed' : 'In Progress'} |
                ${progress.isBookmarked ? 'Bookmarked' : 'Not Bookmarked'}
              </small>
            </div>
            <div>
              ${progress.isCompleted ? '<span class="badge bg-success">Completed</span>' : ''}
              ${progress.isBookmarked ? '<span class="badge bg-warning">Bookmarked</span>' : ''}
            </div>
          </div>
        `;
        progressListElement.appendChild(progressElement);
      });
    }
  });

  // Load categories on page load
  loadCategories();
});