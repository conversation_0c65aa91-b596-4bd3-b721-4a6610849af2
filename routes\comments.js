const express = require('express');
const {
  getComments,
  createComment,
  updateComment,
  deleteComment
} = require('../controllers/commentController');
const { protect, checkOwnership } = require('../middleware/auth');
const Comment = require('../models/Comment');

const router = express.Router({ mergeParams: true });

router.route('/')
  .get(getComments)
  .post(protect, createComment);

router.route('/:commentId')
  .put(protect, checkOwnership(Comment), updateComment)
  .delete(protect, checkOwnership(Comment), deleteComment);

module.exports = router;
