const app = require('./app');
const connectDB = require('./config/db');
const mongoose = require('mongoose');
const config = require('./config/config');
const logger = require('./utils/logger');

// Connect to database
connectDB();

const PORT = config.server.port;

// Start the server
const server = app.listen(PORT, () => {
  logger.info(`Server running in ${config.server.env} mode on port ${PORT}`);

  // Log application startup details
  logger.info('Application started successfully', {
    environment: config.server.env,
    port: PORT,
    nodeVersion: process.version,
    platform: process.platform
  });
});

// Handle graceful shutdown
const gracefulShutdown = async (signal) => {
  logger.info(`${signal} signal received. Shutting down gracefully...`);

  try {
    // Close HTTP server first to stop accepting new connections
    await new Promise((resolve, reject) => {
      server.close((err) => {
        if (err) {
          logger.error('Error closing HTTP server:', { error: err.message });
          reject(err);
        } else {
          logger.info('HTTP server closed successfully');
          resolve();
        }
      });
    });

    // Close database connection
    if (mongoose.connection.readyState !== 0) {
      logger.info('Closing MongoDB connection...');
      await mongoose.connection.close(false);
      logger.info('MongoDB connection closed successfully');
    }

    logger.info('Graceful shutdown completed');
    process.exit(0);
  } catch (err) {
    logger.error('Error during graceful shutdown:', { error: err.message, stack: err.stack });
    process.exit(1);
  }

  // Force close if graceful shutdown takes too long
  setTimeout(() => {
    logger.error('Could not close connections in time, forcefully shutting down');
    process.exit(1);
  }, 10000);
};

// Handle different termination signals
process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
process.on('SIGINT', () => gracefulShutdown('SIGINT'));

// Handle unhandled promise rejections
process.on('unhandledRejection', (err, promise) => {
  logger.error(`Unhandled Promise Rejection: ${err.message}`, {
    stack: err.stack,
    promise: promise.toString().substring(0, 200) // Limit promise string length
  });

  // Don't immediately exit in production to allow pending requests to complete
  if (config.server.isDevelopment) {
    server.close(() => process.exit(1));
  }
});

// Handle uncaught exceptions
process.on('uncaughtException', (err) => {
  logger.error(`Uncaught Exception: ${err.message}`, { stack: err.stack });

  // For uncaught exceptions, we should exit as the application state might be corrupted
  server.close(() => process.exit(1));
});
