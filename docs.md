# Dokumentasi API

Dokumentasi lengkap untuk API REST yang dibangun dengan Node.js, Express, dan <PERSON>.

## Daftar Isi

- [Persyaratan Sistem](#persyaratan-sistem)
- [Deployment](#deployment)
  - [Deployment Lokal](#deployment-lokal)
  - [Deployment ke Heroku](#deployment-ke-heroku)
  - [Deployment ke VPS/Server](#deployment-ke-vpsserver)
- [Varia<PERSON>](#variabel-lingkungan)
- [Keamanan](#keamanan)
- [Struktur Database](#struktur-database)
- [Endpoint API](#endpoint-api)
- [<PERSON><PERSON><PERSON>rror](#penanganan-error)
- [P<PERSON><PERSON><PERSON><PERSON>](#pemeliharaan)

## Persyaratan Sistem

- Node.js (versi 14 atau lebih tinggi)
- MongoDB Atlas (atau MongoDB lokal)
- NPM atau Yarn
- Git

## Deployment

### Deployment Lokal

1. Clone repositori:

```bash
git clone <url-repositori>
cd API-project
```

2. Instal dependensi:

```bash
npm install
```

3. Buat file `.env` di root proyek:

```
PORT=5000
NODE_ENV=development
MONGODB_URI=mongodb+srv://<username>:<password>@<cluster>.mongodb.net/<database>?retryWrites=true&w=majority
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRE=30d
JWT_REFRESH_SECRET=your_jwt_refresh_secret_key
JWT_REFRESH_EXPIRE=7d
```

4. Jalankan server dalam mode pengembangan:

```bash
npm run dev
```

5. Jalankan server dalam mode produksi:

```bash
npm start
```

### Deployment ke Heroku

1. Buat akun Heroku dan instal Heroku CLI.

2. Login ke Heroku CLI:

```bash
heroku login
```

3. Buat aplikasi Heroku:

```bash
heroku create nama-aplikasi-anda
```

4. Tambahkan MongoDB Atlas sebagai add-on atau gunakan layanan eksternal:

```bash
# Jika menggunakan MongoDB Atlas eksternal, tambahkan URI sebagai variabel lingkungan
heroku config:set MONGODB_URI=mongodb+srv://<username>:<password>@<cluster>.mongodb.net/<database>?retryWrites=true&w=majority
```

5. Tambahkan variabel lingkungan lainnya:

```bash
heroku config:set NODE_ENV=production
heroku config:set JWT_SECRET=your_jwt_secret_key
heroku config:set JWT_EXPIRE=30d
heroku config:set JWT_REFRESH_SECRET=your_jwt_refresh_secret_key
heroku config:set JWT_REFRESH_EXPIRE=7d
```

6. Deploy aplikasi:

```bash
git push heroku main
```

7. Buka aplikasi:

```bash
heroku open
```

### Deployment ke VPS/Server

1. Siapkan server dengan Ubuntu/Debian:

```bash
# Update sistem
sudo apt update && sudo apt upgrade -y

# Instal Node.js dan npm
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs

# Instal PM2 untuk manajemen proses
sudo npm install pm2 -g
```

2. Clone repositori:

```bash
git clone <url-repositori>
cd API-project
```

3. Instal dependensi:

```bash
npm install --production
```

4. Buat file `.env`:

```bash
cat > .env << EOL
PORT=5000
NODE_ENV=production
MONGODB_URI=mongodb+srv://<username>:<password>@<cluster>.mongodb.net/<database>?retryWrites=true&w=majority
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRE=30d
JWT_REFRESH_SECRET=your_jwt_refresh_secret_key
JWT_REFRESH_EXPIRE=7d
EOL
```

5. Jalankan aplikasi dengan PM2:

```bash
pm2 start server.js --name api-project
pm2 save
pm2 startup
```

6. Konfigurasi Nginx sebagai reverse proxy:

```bash
sudo apt install nginx -y

# Buat konfigurasi Nginx
sudo nano /etc/nginx/sites-available/api-project

# Tambahkan konfigurasi berikut
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }
}

# Aktifkan konfigurasi
sudo ln -s /etc/nginx/sites-available/api-project /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

7. Tambahkan SSL dengan Certbot:

```bash
sudo apt install certbot python3-certbot-nginx -y
sudo certbot --nginx -d your-domain.com
```

## Variabel Lingkungan

| Variabel | Deskripsi | Contoh |
|----------|-----------|--------|
| PORT | Port server | 5000 |
| NODE_ENV | Lingkungan aplikasi | production, development |
| MONGODB_URI | URI koneksi MongoDB | mongodb+srv://... |
| JWT_SECRET | Kunci rahasia untuk JWT | string acak |
| JWT_EXPIRE | Masa berlaku token JWT | 30d, 24h |
| JWT_REFRESH_SECRET | Kunci rahasia untuk refresh token | string acak |
| JWT_REFRESH_EXPIRE | Masa berlaku refresh token | 7d, 30d |

## Keamanan

API ini mengimplementasikan beberapa fitur keamanan:

1. **Helmet** - Mengamankan aplikasi Express dengan berbagai HTTP header
2. **Rate Limiting** - Membatasi jumlah permintaan dari satu IP
3. **JWT Authentication** - Mengamankan endpoint dengan JSON Web Tokens
4. **Password Hashing** - Menggunakan bcrypt untuk hashing password
5. **CORS** - Mengonfigurasi Cross-Origin Resource Sharing
6. **Input Validation** - Validasi input untuk mencegah injeksi

## Struktur Database

### Model User

```javascript
{
  username: String,
  email: String,
  password: String (hashed),
  createdAt: Date,
  updatedAt: Date
}
```

### Model Post

```javascript
{
  title: String,
  content: String,
  image: String,
  userId: ObjectId (ref: User),
  createdAt: Date,
  updatedAt: Date
}
```

### Model Comment

```javascript
{
  content: String,
  userId: ObjectId (ref: User),
  postId: ObjectId (ref: Post),
  createdAt: Date,
  updatedAt: Date
}
```

## Endpoint API

Lihat [README.md](README.md#endpoint-api) untuk daftar lengkap endpoint API.

## Penanganan Error

API ini menggunakan middleware penanganan error terpusat yang menangani berbagai jenis error:

- Validation errors (400 Bad Request)
- Authentication errors (401 Unauthorized)
- Authorization errors (403 Forbidden)
- Not found errors (404 Not Found)
- Server errors (500 Internal Server Error)

Format respons error:

```json
{
  "success": false,
  "error": "Pesan error yang deskriptif"
}
```

## Pemeliharaan

### Backup Database

Lakukan backup database secara berkala:

```bash
# Untuk MongoDB Atlas, gunakan mongodump
mongodump --uri="mongodb+srv://<username>:<password>@<cluster>.mongodb.net/<database>" --out=./backup/$(date +%Y-%m-%d)
```

### Monitoring

Gunakan PM2 untuk monitoring aplikasi:

```bash
pm2 monit
pm2 logs
```

### Update Dependensi

Perbarui dependensi secara berkala:

```bash
npm outdated
npm update
```

Untuk update major version:

```bash
npx npm-check-updates -u
npm install
```
