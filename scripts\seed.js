/**
 * Database seeding script
 * Populates the database with initial data for development or testing
 */
const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
const config = require('../config/config');
const User = require('../models/User');
const Post = require('../models/Post');
const Comment = require('../models/Comment');
const Category = require('../models/Category');
const Learning = require('../models/Learning');
const connectDB = require('../config/db');
const logger = require('../utils/logger');

// Sample data
const users = [
  {
    username: 'admin',
    email: '<EMAIL>',
    password: 'password123'
  },
  {
    username: 'user1',
    email: '<EMAIL>',
    password: 'password123'
  },
  {
    username: 'user2',
    email: '<EMAIL>',
    password: 'password123'
  }
];

const posts = [
  {
    title: 'First Post',
    content: 'This is the first post content.'
  },
  {
    title: 'Second Post',
    content: 'This is the second post content.'
  },
  {
    title: 'Third Post',
    content: 'This is the third post content.'
  }
];

const comments = [
  {
    content: 'Great post!'
  },
  {
    content: 'I learned a lot from this.'
  },
  {
    content: 'Thanks for sharing!'
  }
];

const categories = [
  {
    name: 'Programming',
    description: 'Learn programming languages and concepts',
    color: '#3B82F6',
    icon: 'code'
  },
  {
    name: 'Web Development',
    description: 'Frontend and backend web development',
    color: '#10B981',
    icon: 'globe'
  },
  {
    name: 'Data Science',
    description: 'Data analysis, machine learning, and AI',
    color: '#8B5CF6',
    icon: 'chart'
  },
  {
    name: 'Mobile Development',
    description: 'iOS and Android app development',
    color: '#F59E0B',
    icon: 'mobile'
  },
  {
    name: 'DevOps',
    description: 'Deployment, CI/CD, and infrastructure',
    color: '#EF4444',
    icon: 'server'
  }
];

const learningContent = [
  {
    title: 'Introduction to JavaScript',
    content: `JavaScript is a versatile programming language that powers the web. Originally created to make web pages interactive, JavaScript has evolved into a full-stack development language.

## What is JavaScript?

JavaScript is a high-level, interpreted programming language that conforms to the ECMAScript specification. It's characterized by:

- **Dynamic typing**: Variables don't need explicit type declarations
- **First-class functions**: Functions can be assigned to variables, passed as arguments
- **Prototype-based object orientation**: Objects can inherit directly from other objects
- **Event-driven programming**: Responds to user interactions and system events

## Basic Syntax

Here are some fundamental JavaScript concepts:

### Variables
\`\`\`javascript
let name = "John";
const age = 25;
var city = "New York";
\`\`\`

### Functions
\`\`\`javascript
function greet(name) {
    return "Hello, " + name + "!";
}

const greetArrow = (name) => \`Hello, \${name}!\`;
\`\`\`

### Objects
\`\`\`javascript
const person = {
    name: "Alice",
    age: 30,
    greet() {
        console.log(\`Hi, I'm \${this.name}\`);
    }
};
\`\`\`

## Modern JavaScript Features

ES6+ introduced many powerful features:

- **Arrow functions**: Shorter function syntax
- **Template literals**: String interpolation with backticks
- **Destructuring**: Extract values from arrays and objects
- **Modules**: Import and export functionality
- **Promises**: Handle asynchronous operations
- **Async/await**: Cleaner asynchronous code

## Conclusion

JavaScript is essential for modern web development. Whether you're building interactive websites, server-side applications with Node.js, or mobile apps, JavaScript skills are invaluable.`,
    summary: 'Learn the fundamentals of JavaScript programming language, including syntax, functions, objects, and modern ES6+ features.',
    difficulty: 'beginner',
    estimatedTime: 15
  },
  {
    title: 'Building REST APIs with Node.js',
    content: `REST APIs are the backbone of modern web applications. In this comprehensive guide, we'll learn how to build robust REST APIs using Node.js and Express.

## What is a REST API?

REST (Representational State Transfer) is an architectural style for designing networked applications. A REST API uses HTTP methods to perform operations on resources.

### HTTP Methods
- **GET**: Retrieve data
- **POST**: Create new data
- **PUT**: Update existing data
- **DELETE**: Remove data

## Setting Up Node.js and Express

First, let's set up our development environment:

\`\`\`bash
npm init -y
npm install express mongoose dotenv cors helmet
npm install -D nodemon
\`\`\`

### Basic Express Server
\`\`\`javascript
const express = require('express');
const app = express();

app.use(express.json());

app.get('/api/users', (req, res) => {
    res.json({ message: 'Get all users' });
});

app.listen(3000, () => {
    console.log('Server running on port 3000');
});
\`\`\`

## Database Integration with MongoDB

Using Mongoose for MongoDB integration:

\`\`\`javascript
const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
    name: { type: String, required: true },
    email: { type: String, required: true, unique: true },
    createdAt: { type: Date, default: Date.now }
});

const User = mongoose.model('User', userSchema);
\`\`\`

## CRUD Operations

### Create User
\`\`\`javascript
app.post('/api/users', async (req, res) => {
    try {
        const user = new User(req.body);
        await user.save();
        res.status(201).json(user);
    } catch (error) {
        res.status(400).json({ error: error.message });
    }
});
\`\`\`

### Read Users
\`\`\`javascript
app.get('/api/users', async (req, res) => {
    try {
        const users = await User.find();
        res.json(users);
    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});
\`\`\`

## Authentication and Security

Implementing JWT authentication:

\`\`\`javascript
const jwt = require('jsonwebtoken');

const authenticateToken = (req, res, next) => {
    const token = req.header('Authorization')?.replace('Bearer ', '');

    if (!token) {
        return res.status(401).json({ error: 'Access denied' });
    }

    try {
        const decoded = jwt.verify(token, process.env.JWT_SECRET);
        req.user = decoded;
        next();
    } catch (error) {
        res.status(403).json({ error: 'Invalid token' });
    }
};
\`\`\`

## Best Practices

1. **Use proper HTTP status codes**
2. **Implement error handling middleware**
3. **Validate input data**
4. **Use environment variables for configuration**
5. **Implement rate limiting**
6. **Add proper logging**
7. **Use HTTPS in production**

## Conclusion

Building REST APIs with Node.js and Express provides a solid foundation for backend development. Focus on clean code, proper error handling, and security best practices.`,
    summary: 'Complete guide to building REST APIs with Node.js, Express, and MongoDB. Covers CRUD operations, authentication, and best practices.',
    difficulty: 'intermediate',
    estimatedTime: 30
  },
  {
    title: 'React Hooks Deep Dive',
    content: `React Hooks revolutionized how we write React components. This deep dive explores the most important hooks and how to use them effectively.

## Introduction to Hooks

Hooks are functions that let you "hook into" React state and lifecycle features from function components. They were introduced in React 16.8.

### Rules of Hooks
1. Only call hooks at the top level
2. Only call hooks from React functions

## useState Hook

The most basic hook for managing component state:

\`\`\`javascript
import React, { useState } from 'react';

function Counter() {
    const [count, setCount] = useState(0);

    return (
        <div>
            <p>Count: {count}</p>
            <button onClick={() => setCount(count + 1)}>
                Increment
            </button>
        </div>
    );
}
\`\`\`

### Complex State
\`\`\`javascript
const [user, setUser] = useState({
    name: '',
    email: '',
    age: 0
});

const updateUser = (field, value) => {
    setUser(prev => ({
        ...prev,
        [field]: value
    }));
};
\`\`\`

## useEffect Hook

Handle side effects in function components:

\`\`\`javascript
import React, { useState, useEffect } from 'react';

function UserProfile({ userId }) {
    const [user, setUser] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        const fetchUser = async () => {
            try {
                const response = await fetch(\`/api/users/\${userId}\`);
                const userData = await response.json();
                setUser(userData);
            } catch (error) {
                console.error('Error fetching user:', error);
            } finally {
                setLoading(false);
            }
        };

        fetchUser();
    }, [userId]); // Dependency array

    if (loading) return <div>Loading...</div>;

    return (
        <div>
            <h1>{user?.name}</h1>
            <p>{user?.email}</p>
        </div>
    );
}
\`\`\`

## useContext Hook

Share data across components without prop drilling:

\`\`\`javascript
import React, { createContext, useContext, useState } from 'react';

const ThemeContext = createContext();

function ThemeProvider({ children }) {
    const [theme, setTheme] = useState('light');

    return (
        <ThemeContext.Provider value={{ theme, setTheme }}>
            {children}
        </ThemeContext.Provider>
    );
}

function ThemedButton() {
    const { theme, setTheme } = useContext(ThemeContext);

    return (
        <button
            style={{
                background: theme === 'light' ? '#fff' : '#333',
                color: theme === 'light' ? '#333' : '#fff'
            }}
            onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
        >
            Toggle Theme
        </button>
    );
}
\`\`\`

## Custom Hooks

Create reusable stateful logic:

\`\`\`javascript
function useLocalStorage(key, initialValue) {
    const [storedValue, setStoredValue] = useState(() => {
        try {
            const item = window.localStorage.getItem(key);
            return item ? JSON.parse(item) : initialValue;
        } catch (error) {
            return initialValue;
        }
    });

    const setValue = (value) => {
        try {
            setStoredValue(value);
            window.localStorage.setItem(key, JSON.stringify(value));
        } catch (error) {
            console.error('Error saving to localStorage:', error);
        }
    };

    return [storedValue, setValue];
}

// Usage
function Settings() {
    const [settings, setSettings] = useLocalStorage('settings', {
        notifications: true,
        theme: 'light'
    });

    return (
        <div>
            <label>
                <input
                    type="checkbox"
                    checked={settings.notifications}
                    onChange={(e) => setSettings({
                        ...settings,
                        notifications: e.target.checked
                    })}
                />
                Enable Notifications
            </label>
        </div>
    );
}
\`\`\`

## Performance Optimization

### useMemo
Memoize expensive calculations:

\`\`\`javascript
function ExpensiveComponent({ items, filter }) {
    const filteredItems = useMemo(() => {
        return items.filter(item =>
            item.name.toLowerCase().includes(filter.toLowerCase())
        );
    }, [items, filter]);

    return (
        <ul>
            {filteredItems.map(item => (
                <li key={item.id}>{item.name}</li>
            ))}
        </ul>
    );
}
\`\`\`

### useCallback
Memoize functions:

\`\`\`javascript
function Parent() {
    const [count, setCount] = useState(0);
    const [name, setName] = useState('');

    const handleClick = useCallback(() => {
        setCount(c => c + 1);
    }, []); // No dependencies, function never changes

    return (
        <div>
            <input
                value={name}
                onChange={(e) => setName(e.target.value)}
            />
            <Child onClick={handleClick} />
            <p>Count: {count}</p>
        </div>
    );
}
\`\`\`

## Conclusion

React Hooks provide a powerful way to manage state and side effects in function components. Master these patterns to write cleaner, more maintainable React code.`,
    summary: 'Comprehensive guide to React Hooks including useState, useEffect, useContext, custom hooks, and performance optimization techniques.',
    difficulty: 'intermediate',
    estimatedTime: 25
  }
];

// Seed function
const seedDatabase = async () => {
  try {
    // Connect to database
    await connectDB();

    logger.info('Connected to MongoDB. Starting seed process...');

    // Clear existing data
    await User.deleteMany({});
    await Post.deleteMany({});
    await Comment.deleteMany({});
    await Category.deleteMany({});
    await Learning.deleteMany({});

    logger.info('Cleared existing data');

    // Create users with hashed passwords
    const createdUsers = [];
    for (const user of users) {
      // Hash password
      const salt = await bcrypt.genSalt(10);
      const hashedPassword = await bcrypt.hash(user.password, salt);

      const newUser = await User.create({
        ...user,
        password: hashedPassword
      });

      createdUsers.push(newUser);
    }

    logger.info(`Created ${createdUsers.length} users`);

    // Create posts and assign to users
    const createdPosts = [];
    for (let i = 0; i < posts.length; i++) {
      const userIndex = i % createdUsers.length;

      const newPost = await Post.create({
        ...posts[i],
        userId: createdUsers[userIndex]._id
      });

      createdPosts.push(newPost);
    }

    logger.info(`Created ${createdPosts.length} posts`);

    // Create comments and assign to posts and users
    const createdComments = [];
    for (let i = 0; i < comments.length; i++) {
      const postIndex = i % createdPosts.length;
      const userIndex = (i + 1) % createdUsers.length;

      const newComment = await Comment.create({
        ...comments[i],
        postId: createdPosts[postIndex]._id,
        userId: createdUsers[userIndex]._id
      });

      createdComments.push(newComment);
    }

    logger.info(`Created ${createdComments.length} comments`);

    // Create categories
    const createdCategories = [];
    for (const category of categories) {
      const newCategory = await Category.create(category);
      createdCategories.push(newCategory);
    }

    logger.info(`Created ${createdCategories.length} categories`);

    // Create learning content and assign to users and categories
    const createdLearning = [];
    for (let i = 0; i < learningContent.length; i++) {
      const userIndex = i % createdUsers.length;
      const categoryIndices = [
        i % createdCategories.length,
        (i + 1) % createdCategories.length
      ];

      const newLearning = await Learning.create({
        ...learningContent[i],
        userId: createdUsers[userIndex]._id,
        categories: [
          createdCategories[categoryIndices[0]]._id,
          createdCategories[categoryIndices[1]]._id
        ]
      });

      createdLearning.push(newLearning);
    }

    logger.info(`Created ${createdLearning.length} learning content items`);
    logger.info('Database seeding completed successfully');

    // Disconnect from database
    await mongoose.disconnect();
    logger.info('Disconnected from MongoDB');

    process.exit(0);
  } catch (error) {
    logger.error('Error seeding database:', { error: error.message, stack: error.stack });

    // Disconnect from database
    if (mongoose.connection.readyState !== 0) {
      await mongoose.disconnect();
    }

    process.exit(1);
  }
};

// Run the seed function
seedDatabase();
